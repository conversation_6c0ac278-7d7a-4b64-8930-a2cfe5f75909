<?php
/**
 * لوحة تحكم المسؤول الرئيسية
 * Main Admin Dashboard
 */

// تعريف ثابت الوصول
define('APP_ACCESS', true);

// تضمين الملفات المطلوبة
require_once '../backend/config/config.php';
require_once '../backend/config/database.php';
require_once '../backend/utils/helpers.php';
require_once '../backend/middleware/auth.php';

// بدء الجلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// الحصول على الإحصائيات
$db = getDB();

// إحصائيات المستخدمين
$totalUsers = $db->count('users');
$activeUsers = $db->count('users', ['is_online = 1']);
$newUsersToday = $db->count('users', ['DATE(created_at) = CURDATE()']);

// إحصائيات الرسائل
$totalMessages = $db->count('messages');
$messagesToday = $db->count('messages', ['DATE(sent_at) = CURDATE()']);

// إحصائيات المكالمات
$totalCalls = $db->count('calls');
$callsToday = $db->count('calls', ['DATE(started_at) = CURDATE()']);

// إحصائيات المحادثات
$totalConversations = $db->count('conversations');
$activeConversations = $db->count('conversations', ['is_active = 1']);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المسؤول - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .stat-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .page-title {
            color: #333;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-comments"></i>
                        <?php echo APP_NAME; ?>
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة القيادة
                        </a>
                        <a class="nav-link" href="users/">
                            <i class="fas fa-users me-2"></i>
                            إدارة المستخدمين
                        </a>
                        <a class="nav-link" href="messages/">
                            <i class="fas fa-envelope me-2"></i>
                            إدارة الرسائل
                        </a>
                        <a class="nav-link" href="features/">
                            <i class="fas fa-cogs me-2"></i>
                            إدارة الميزات
                        </a>
                        <a class="nav-link" href="reports/">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-sliders-h me-2"></i>
                            الإعدادات
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white rounded mb-4">
                    <div class="container-fluid">
                        <span class="navbar-brand">مرحباً، <?php echo $_SESSION['admin_name'] ?? 'المسؤول'; ?></span>
                        <div class="navbar-nav ms-auto">
                            <span class="nav-item nav-link">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo date('Y-m-d H:i'); ?>
                            </span>
                        </div>
                    </div>
                </nav>
                
                <!-- Page Title -->
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة القيادة
                </h1>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-3"></i>
                                <h3><?php echo number_format($totalUsers); ?></h3>
                                <p class="mb-0">إجمالي المستخدمين</p>
                                <small>+<?php echo $newUsersToday; ?> اليوم</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card-success">
                            <div class="card-body text-center">
                                <i class="fas fa-user-check fa-2x mb-3"></i>
                                <h3><?php echo number_format($activeUsers); ?></h3>
                                <p class="mb-0">المستخدمون النشطون</p>
                                <small>متصل الآن</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-envelope fa-2x mb-3"></i>
                                <h3><?php echo number_format($totalMessages); ?></h3>
                                <p class="mb-0">إجمالي الرسائل</p>
                                <small>+<?php echo $messagesToday; ?> اليوم</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card-info">
                            <div class="card-body text-center">
                                <i class="fas fa-phone fa-2x mb-3"></i>
                                <h3><?php echo number_format($totalCalls); ?></h3>
                                <p class="mb-0">إجمالي المكالمات</p>
                                <small>+<?php echo $callsToday; ?> اليوم</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    نشاط المستخدمين (آخر 7 أيام)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="userActivityChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    توزيع أنواع الحسابات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="accountTypesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    النشاط الأخير
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>المستخدم</th>
                                                <th>النشاط</th>
                                                <th>الوقت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // الحصول على آخر الأنشطة
                                            $recentActivities = $db->select("
                                                SELECT al.*, u.name, u.phone 
                                                FROM activity_logs al 
                                                LEFT JOIN users u ON al.user_id = u.id 
                                                ORDER BY al.created_at DESC 
                                                LIMIT 10
                                            ");
                                            
                                            foreach ($recentActivities as $activity):
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($activity['name'] ?? 'غير معروف'); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($activity['phone'] ?? ''); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo htmlspecialchars($activity['action']); ?></span>
                                                </td>
                                                <td>
                                                    <small><?php echo formatTime($activity['created_at'], 'Y-m-d H:i'); ?></small>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    تنبيهات النظام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    النظام يعمل بشكل طبيعي
                                </div>
                                
                                <div class="alert alert-warning">
                                    <i class="fas fa-database me-2"></i>
                                    حجم قاعدة البيانات: 85%
                                </div>
                                
                                <div class="alert alert-success">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    آخر نسخة احتياطية: اليوم
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Charts -->
    <script>
        // رسم بياني لنشاط المستخدمين
        const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
        new Chart(userActivityCtx, {
            type: 'line',
            data: {
                labels: ['6 أيام', '5 أيام', '4 أيام', '3 أيام', 'أمس', 'اليوم'],
                datasets: [{
                    label: 'مستخدمون جدد',
                    data: [12, 19, 3, 5, 2, 8],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // رسم بياني لأنواع الحسابات
        const accountTypesCtx = document.getElementById('accountTypesChart').getContext('2d');
        new Chart(accountTypesCtx, {
            type: 'doughnut',
            data: {
                labels: ['مجاني', 'VIP', 'Enterprise'],
                datasets: [{
                    data: [<?php 
                        echo $db->count('users', ['account_type = "free"']) . ',';
                        echo $db->count('users', ['account_type = "vip"']) . ',';
                        echo $db->count('users', ['account_type = "enterprise"']);
                    ?>],
                    backgroundColor: ['#667eea', '#56ab2f', '#f093fb']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html>
