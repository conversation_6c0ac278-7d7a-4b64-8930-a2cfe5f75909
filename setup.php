<?php
/**
 * ملف إعداد المشروع
 * Project Setup Script
 */

// تعريف ثابت الوصول
define('APP_ACCESS', true);

// تضمين الملفات المطلوبة
require_once 'backend/config/config.php';

// التحقق من وجود قاعدة البيانات
function checkDatabaseConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// إنشاء قاعدة البيانات
function createDatabase() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// تنفيذ ملف SQL
function executeSQLFile($filename) {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql = file_get_contents($filename);
        $pdo->exec($sql);
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// إنشاء مجلدات النظام
function createSystemDirectories() {
    $directories = [
        'backend/logs',
        'backend/cache',
        'backend/uploads',
        'backend/uploads/images',
        'backend/uploads/videos',
        'backend/uploads/audio',
        'backend/uploads/documents',
        'backend/uploads/avatars',
        'backend/backups'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    return true;
}

// إنشاء ملف .env
function createEnvFile() {
    if (!file_exists('.env') && file_exists('backend/.env.example')) {
        copy('backend/.env.example', '.env');
        return true;
    }
    return file_exists('.env');
}

// إنشاء مستخدم مسؤول افتراضي
function createDefaultAdmin() {
    try {
        require_once 'backend/config/database.php';
        require_once 'backend/utils/helpers.php';
        
        $db = getDB();
        
        // التحقق من وجود مسؤول
        $existingAdmin = $db->selectOne("SELECT id FROM users WHERE account_type = 'enterprise' LIMIT 1");
        
        if (!$existingAdmin) {
            $adminData = [
                'phone' => '+************',
                'email' => '<EMAIL>',
                'name' => 'مسؤول النظام',
                'password' => hashPassword('admin123'),
                'account_type' => 'enterprise',
                'is_verified' => 1,
                'settings' => json_encode([
                    'notifications' => true,
                    'privacy' => [
                        'last_seen' => 'everyone',
                        'profile_photo' => 'everyone',
                        'about' => 'everyone'
                    ]
                ])
            ];
            
            $query = "INSERT INTO users (phone, email, name, password, account_type, is_verified, settings) 
                      VALUES (:phone, :email, :name, :password, :account_type, :is_verified, :settings)";
            
            $adminId = $db->insert($query, $adminData);
            
            return $adminId !== false;
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// معالجة الإعداد
$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من متطلبات النظام
            if (version_compare(PHP_VERSION, '8.1.0', '<')) {
                $errors[] = 'يتطلب PHP 8.1 أو أحدث';
            }
            
            if (!extension_loaded('pdo')) {
                $errors[] = 'امتداد PDO غير مثبت';
            }
            
            if (!extension_loaded('pdo_mysql')) {
                $errors[] = 'امتداد PDO MySQL غير مثبت';
            }
            
            if (!extension_loaded('openssl')) {
                $errors[] = 'امتداد OpenSSL غير مثبت';
            }
            
            if (!extension_loaded('json')) {
                $errors[] = 'امتداد JSON غير مثبت';
            }
            
            if (empty($errors)) {
                header('Location: setup.php?step=2');
                exit;
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            if (!checkDatabaseConnection()) {
                $errors[] = 'فشل الاتصال بقاعدة البيانات';
            } else {
                if (createDatabase()) {
                    $success[] = 'تم إنشاء قاعدة البيانات بنجاح';
                    header('Location: setup.php?step=3');
                    exit;
                } else {
                    $errors[] = 'فشل إنشاء قاعدة البيانات';
                }
            }
            break;
            
        case 3:
            // تنفيذ مخطط قاعدة البيانات
            if (executeSQLFile('database/schema.sql')) {
                $success[] = 'تم إنشاء جداول قاعدة البيانات بنجاح';
                header('Location: setup.php?step=4');
                exit;
            } else {
                $errors[] = 'فشل إنشاء جداول قاعدة البيانات';
            }
            break;
            
        case 4:
            // إنشاء المجلدات والملفات
            createSystemDirectories();
            createEnvFile();
            
            if (createDefaultAdmin()) {
                $success[] = 'تم إنشاء المسؤول الافتراضي بنجاح';
                header('Location: setup.php?step=5');
                exit;
            } else {
                $errors[] = 'فشل إنشاء المسؤول الافتراضي';
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .setup-body {
            padding: 40px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }
        
        .step.active {
            background: #667eea;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 2px;
            background: #e9ecef;
        }
        
        .step.completed:not(:last-child)::after {
            background: #28a745;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <!-- Header -->
        <div class="setup-header">
            <h1>
                <i class="fas fa-cogs me-2"></i>
                إعداد <?php echo APP_NAME; ?>
            </h1>
            <p class="mb-0">مرحباً بك في معالج الإعداد</p>
        </div>
        
        <!-- Body -->
        <div class="setup-body">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
            </div>
            
            <!-- Errors -->
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>أخطاء:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <!-- Success -->
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>نجح:</h5>
                    <ul class="mb-0">
                        <?php foreach ($success as $msg): ?>
                            <li><?php echo htmlspecialchars($msg); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <!-- Step Content -->
            <?php switch ($step): 
                case 1: ?>
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-server me-2"></i>التحقق من متطلبات النظام</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>إصدار PHP:</h6>
                                    <p class="<?php echo version_compare(PHP_VERSION, '8.1.0', '>=') ? 'text-success' : 'text-danger'; ?>">
                                        <i class="fas <?php echo version_compare(PHP_VERSION, '8.1.0', '>=') ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                                        <?php echo PHP_VERSION; ?> (مطلوب 8.1+)
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6>امتدادات PHP:</h6>
                                    <?php 
                                    $extensions = ['pdo', 'pdo_mysql', 'openssl', 'json', 'mbstring', 'gd'];
                                    foreach ($extensions as $ext): 
                                    ?>
                                        <p class="<?php echo extension_loaded($ext) ? 'text-success' : 'text-danger'; ?>">
                                            <i class="fas <?php echo extension_loaded($ext) ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                                            <?php echo $ext; ?>
                                        </p>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    التالي
                                </button>
                            </form>
                        </div>
                    </div>
                    <?php break;
                    
                case 2: ?>
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h4>
                        </div>
                        <div class="card-body">
                            <p>سيتم إنشاء قاعدة البيانات باستخدام الإعدادات التالية:</p>
                            <ul>
                                <li><strong>الخادم:</strong> <?php echo DB_HOST; ?></li>
                                <li><strong>اسم قاعدة البيانات:</strong> <?php echo DB_NAME; ?></li>
                                <li><strong>المستخدم:</strong> <?php echo DB_USER; ?></li>
                            </ul>
                            
                            <form method="POST">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-database me-2"></i>
                                    إنشاء قاعدة البيانات
                                </button>
                            </form>
                        </div>
                    </div>
                    <?php break;
                    
                case 3: ?>
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-table me-2"></i>إنشاء جداول قاعدة البيانات</h4>
                        </div>
                        <div class="card-body">
                            <p>سيتم إنشاء جميع الجداول المطلوبة للنظام.</p>
                            
                            <form method="POST">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-table me-2"></i>
                                    إنشاء الجداول
                                </button>
                            </form>
                        </div>
                    </div>
                    <?php break;
                    
                case 4: ?>
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-user-shield me-2"></i>إنشاء المسؤول الافتراضي</h4>
                        </div>
                        <div class="card-body">
                            <p>سيتم إنشاء حساب مسؤول افتراضي بالبيانات التالية:</p>
                            <ul>
                                <li><strong>الهاتف:</strong> +************</li>
                                <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                                <li><strong>كلمة المرور:</strong> admin123</li>
                            </ul>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                يرجى تغيير كلمة المرور بعد تسجيل الدخول لأول مرة.
                            </div>
                            
                            <form method="POST">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2"></i>
                                    إنشاء المسؤول
                                </button>
                            </form>
                        </div>
                    </div>
                    <?php break;
                    
                case 5: ?>
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4><i class="fas fa-check-circle me-2"></i>تم الإعداد بنجاح!</h4>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h5>تم إعداد النظام بنجاح!</h5>
                            <p>يمكنك الآن الوصول إلى لوحة تحكم المسؤول.</p>
                            
                            <div class="d-grid gap-2">
                                <a href="admin/login.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    الذهاب إلى لوحة التحكم
                                </a>
                                <a href="backend/api/" class="btn btn-outline-primary">
                                    <i class="fas fa-code me-2"></i>
                                    اختبار API
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php break;
            endswitch; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
