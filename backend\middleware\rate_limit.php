<?php
/**
 * وسطاء تحديد المعدل
 * Rate Limiting Middleware
 */

// منع الوصول المباشر
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

/**
 * فئة تحديد المعدل
 */
class RateLimiter {
    private $db;
    private $cacheDir;
    
    public function __construct() {
        $this->db = getDB();
        $this->cacheDir = __DIR__ . '/../cache/rate_limits/';
        
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * التحقق من حد المعدل
     */
    public function checkLimit($identifier, $maxAttempts = 60, $timeWindow = 3600, $endpoint = 'general') {
        if (!RATE_LIMIT_ENABLED) {
            return true;
        }
        
        $key = $this->generateKey($identifier, $endpoint);
        $attempts = $this->getAttempts($key);
        
        if ($attempts >= $maxAttempts) {
            $this->logRateLimitExceeded($identifier, $endpoint, $attempts);
            return false;
        }
        
        $this->incrementAttempts($key, $timeWindow);
        return true;
    }
    
    /**
     * التحقق من حد المعدل للمصادقة
     */
    public function checkAuthLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
        return $this->checkLimit($identifier, $maxAttempts, $timeWindow, 'auth');
    }
    
    /**
     * التحقق من حد المعدل للرسائل
     */
    public function checkMessageLimit($userId, $maxMessages = 100, $timeWindow = 3600) {
        return $this->checkLimit("user_{$userId}", $maxMessages, $timeWindow, 'messages');
    }
    
    /**
     * التحقق من حد المعدل للمكالمات
     */
    public function checkCallLimit($userId, $maxCalls = 20, $timeWindow = 3600) {
        return $this->checkLimit("user_{$userId}", $maxCalls, $timeWindow, 'calls');
    }
    
    /**
     * التحقق من حد المعدل للذكاء الاصطناعي
     */
    public function checkAILimit($userId, $maxRequests = null, $timeWindow = 86400) {
        $user = getUserById($userId);
        
        if (!$user) {
            return false;
        }
        
        // تحديد الحد حسب نوع الحساب
        if ($maxRequests === null) {
            $planFeatures = getPlanFeatures($user['account_type']);
            $maxRequests = $planFeatures['ai_requests_per_day'] ?? 10;
            
            if ($maxRequests === 0) {
                return true; // بلا حدود
            }
        }
        
        return $this->checkLimit("user_{$userId}", $maxRequests, $timeWindow, 'ai');
    }
    
    /**
     * التحقق من حد المعدل لرفع الملفات
     */
    public function checkUploadLimit($userId, $maxUploads = 50, $timeWindow = 3600) {
        return $this->checkLimit("user_{$userId}", $maxUploads, $timeWindow, 'uploads');
    }
    
    /**
     * إعادة تعيين حد المعدل
     */
    public function resetLimit($identifier, $endpoint = 'general') {
        $key = $this->generateKey($identifier, $endpoint);
        $this->clearAttempts($key);
    }
    
    /**
     * الحصول على معلومات الحد
     */
    public function getLimitInfo($identifier, $endpoint = 'general') {
        $key = $this->generateKey($identifier, $endpoint);
        $attempts = $this->getAttempts($key);
        $resetTime = $this->getResetTime($key);
        
        return [
            'attempts' => $attempts,
            'reset_time' => $resetTime,
            'remaining_time' => max(0, $resetTime - time())
        ];
    }
    
    /**
     * توليد مفتاح التخزين المؤقت
     */
    private function generateKey($identifier, $endpoint) {
        return md5($identifier . '_' . $endpoint);
    }
    
    /**
     * الحصول على عدد المحاولات
     */
    private function getAttempts($key) {
        $file = $this->cacheDir . $key . '.json';
        
        if (!file_exists($file)) {
            return 0;
        }
        
        $data = json_decode(file_get_contents($file), true);
        
        if (!$data || $data['reset_time'] < time()) {
            $this->clearAttempts($key);
            return 0;
        }
        
        return $data['attempts'];
    }
    
    /**
     * زيادة عدد المحاولات
     */
    private function incrementAttempts($key, $timeWindow) {
        $file = $this->cacheDir . $key . '.json';
        $attempts = $this->getAttempts($key);
        
        $data = [
            'attempts' => $attempts + 1,
            'reset_time' => time() + $timeWindow,
            'first_attempt' => $attempts === 0 ? time() : ($this->getFirstAttemptTime($key) ?: time())
        ];
        
        file_put_contents($file, json_encode($data), LOCK_EX);
    }
    
    /**
     * مسح المحاولات
     */
    private function clearAttempts($key) {
        $file = $this->cacheDir . $key . '.json';
        
        if (file_exists($file)) {
            unlink($file);
        }
    }
    
    /**
     * الحصول على وقت إعادة التعيين
     */
    private function getResetTime($key) {
        $file = $this->cacheDir . $key . '.json';
        
        if (!file_exists($file)) {
            return 0;
        }
        
        $data = json_decode(file_get_contents($file), true);
        return $data['reset_time'] ?? 0;
    }
    
    /**
     * الحصول على وقت أول محاولة
     */
    private function getFirstAttemptTime($key) {
        $file = $this->cacheDir . $key . '.json';
        
        if (!file_exists($file)) {
            return null;
        }
        
        $data = json_decode(file_get_contents($file), true);
        return $data['first_attempt'] ?? null;
    }
    
    /**
     * تسجيل تجاوز الحد
     */
    private function logRateLimitExceeded($identifier, $endpoint, $attempts) {
        $logData = [
            'identifier' => $identifier,
            'endpoint' => $endpoint,
            'attempts' => $attempts,
            'ip_address' => getRealIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $logFile = LOG_PATH . 'rate_limits_' . date('Y-m-d') . '.log';
        $logMessage = json_encode($logData) . PHP_EOL;
        
        if (!is_dir(LOG_PATH)) {
            mkdir(LOG_PATH, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * تنظيف الملفات القديمة
     */
    public function cleanup() {
        $files = glob($this->cacheDir . '*.json');
        $now = time();
        
        foreach ($files as $file) {
            $data = json_decode(file_get_contents($file), true);
            
            if (!$data || $data['reset_time'] < $now) {
                unlink($file);
            }
        }
    }
}

// إنشاء مثيل عام
$rateLimiter = new RateLimiter();

/**
 * دالة مساعدة للتحقق من حد المعدل العام
 */
function checkRateLimit($identifier, $maxAttempts = 60, $timeWindow = 3600, $endpoint = 'general') {
    global $rateLimiter;
    return $rateLimiter->checkLimit($identifier, $maxAttempts, $timeWindow, $endpoint);
}

/**
 * دالة مساعدة للتحقق من حد المصادقة
 */
function checkAuthRateLimit($identifier) {
    global $rateLimiter;
    return $rateLimiter->checkAuthLimit($identifier);
}

/**
 * دالة مساعدة لإعادة تعيين الحد
 */
function resetRateLimit($identifier, $endpoint = 'general') {
    global $rateLimiter;
    return $rateLimiter->resetLimit($identifier, $endpoint);
}

/**
 * دالة مساعدة للحصول على معلومات الحد
 */
function getRateLimitInfo($identifier, $endpoint = 'general') {
    global $rateLimiter;
    return $rateLimiter->getLimitInfo($identifier, $endpoint);
}

/**
 * تنظيف دوري للملفات القديمة
 */
function cleanupRateLimits() {
    global $rateLimiter;
    $rateLimiter->cleanup();
}

// تشغيل التنظيف بشكل عشوائي (1% من الطلبات)
if (rand(1, 100) === 1) {
    cleanupRateLimits();
}

?>
