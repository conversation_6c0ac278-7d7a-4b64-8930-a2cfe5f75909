<?php
/**
 * الدوال المساعدة العامة
 * General Helper Functions
 */

// منع الوصول المباشر
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

/**
 * إرجاع استجابة JSON
 */
function jsonResponse($data, $statusCode = 200, $message = null) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'success' => $statusCode >= 200 && $statusCode < 300,
        'status_code' => $statusCode,
        'data' => $data
    ];
    
    if ($message) {
        $response['message'] = $message;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * إرجاع استجابة خطأ
 */
function errorResponse($message, $statusCode = 400, $errors = null) {
    $response = [
        'success' => false,
        'status_code' => $statusCode,
        'message' => $message
    ];
    
    if ($errors) {
        $response['errors'] = $errors;
    }
    
    jsonResponse($response, $statusCode);
}

/**
 * إرجاع استجابة نجاح
 */
function successResponse($data = null, $message = 'Success', $statusCode = 200) {
    jsonResponse($data, $statusCode, $message);
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف
 */
function isValidPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // التحقق من الطول والتنسيق
    return preg_match('/^\+?[1-9]\d{1,14}$/', $phone);
}

/**
 * تنظيف رقم الهاتف
 */
function cleanPhone($phone) {
    // إزالة كل شيء عدا الأرقام والعلامة +
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // إضافة رمز الدولة إذا لم يكن موجوداً
    if (!str_starts_with($phone, '+')) {
        // افتراض رمز السعودية إذا بدأ بـ 05
        if (str_starts_with($phone, '05')) {
            $phone = '+966' . substr($phone, 1);
        } elseif (str_starts_with($phone, '5')) {
            $phone = '+966' . $phone;
        } else {
            $phone = '+' . $phone;
        }
    }
    
    return $phone;
}

/**
 * توليد كود OTP
 */
function generateOTP($length = 6) {
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= random_int(0, 9);
    }
    return $otp;
}

/**
 * توليد رمز عشوائي آمن
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password . PASSWORD_SALT, PASSWORD_ARGON2ID);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password . PASSWORD_SALT, $hash);
}

/**
 * تشفير النص
 */
function encryptText($text) {
    if (!MESSAGE_ENCRYPTION) {
        return $text;
    }
    
    $key = ENCRYPTION_KEY;
    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($text, 'AES-256-CBC', $key, 0, $iv);
    
    return base64_encode($iv . $encrypted);
}

/**
 * فك تشفير النص
 */
function decryptText($encryptedText) {
    if (!MESSAGE_ENCRYPTION) {
        return $encryptedText;
    }
    
    $key = ENCRYPTION_KEY;
    $data = base64_decode($encryptedText);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

/**
 * تنظيف النص من HTML والسكريبت
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة JWT Token
 */
function validateJWT($token) {
    try {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        $header = json_decode(base64_decode($parts[0]), true);
        $payload = json_decode(base64_decode($parts[1]), true);
        $signature = $parts[2];
        
        // التحقق من انتهاء الصلاحية
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }
        
        // التحقق من التوقيع
        $expectedSignature = base64_encode(hash_hmac('sha256', $parts[0] . '.' . $parts[1], JWT_SECRET, true));
        
        return hash_equals($expectedSignature, $signature) ? $payload : false;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * إنشاء JWT Token
 */
function createJWT($payload) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    
    $payload['iat'] = time();
    $payload['exp'] = time() + JWT_EXPIRE;
    $payload = json_encode($payload);
    
    $base64Header = base64_encode($header);
    $base64Payload = base64_encode($payload);
    
    $signature = base64_encode(hash_hmac('sha256', $base64Header . '.' . $base64Payload, JWT_SECRET, true));
    
    return $base64Header . '.' . $base64Payload . '.' . $signature;
}

/**
 * الحصول على عنوان IP الحقيقي
 */
function getRealIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * تسجيل النشاط
 */
function logActivity($userId, $action, $entityType = null, $entityId = null, $details = null) {
    try {
        $db = getDB();
        
        $data = [
            'user_id' => $userId,
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'details' => $details ? json_encode($details) : null,
            'ip_address' => getRealIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $query = "INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent) 
                  VALUES (:user_id, :action, :entity_type, :entity_id, :details, :ip_address, :user_agent)";
        
        return $db->insert($query, $data);
        
    } catch (Exception $e) {
        // تسجيل الخطأ دون إيقاف التطبيق
        error_log("Failed to log activity: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من حد المعدل (Rate Limiting)
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
    if (!RATE_LIMIT_ENABLED) {
        return true;
    }
    
    $cacheKey = "rate_limit_{$identifier}";
    $attempts = getFromCache($cacheKey, 0);
    
    if ($attempts >= $maxAttempts) {
        return false;
    }
    
    setCache($cacheKey, $attempts + 1, $timeWindow);
    return true;
}

/**
 * حفظ في التخزين المؤقت
 */
function setCache($key, $value, $ttl = null) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    $ttl = $ttl ?? CACHE_TTL;
    $cacheFile = getCacheFilePath($key);
    
    $data = [
        'value' => $value,
        'expires' => time() + $ttl
    ];
    
    return file_put_contents($cacheFile, serialize($data), LOCK_EX) !== false;
}

/**
 * الحصول من التخزين المؤقت
 */
function getFromCache($key, $default = null) {
    if (!CACHE_ENABLED) {
        return $default;
    }
    
    $cacheFile = getCacheFilePath($key);
    
    if (!file_exists($cacheFile)) {
        return $default;
    }
    
    $data = unserialize(file_get_contents($cacheFile));
    
    if (!$data || $data['expires'] < time()) {
        unlink($cacheFile);
        return $default;
    }
    
    return $data['value'];
}

/**
 * الحصول على مسار ملف التخزين المؤقت
 */
function getCacheFilePath($key) {
    $cacheDir = __DIR__ . '/../cache/';
    
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    return $cacheDir . md5($key) . '.cache';
}

/**
 * تنسيق الوقت للعرض
 */
function formatTime($timestamp, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($timestamp));
}

/**
 * تحويل حجم الملف إلى نص قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * التحقق من نوع الملف المسموح
 */
function isAllowedFileType($filename, $type = 'image') {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    switch ($type) {
        case 'image':
            return in_array($extension, ALLOWED_IMAGE_TYPES);
        case 'video':
            return in_array($extension, ALLOWED_VIDEO_TYPES);
        case 'audio':
            return in_array($extension, ALLOWED_AUDIO_TYPES);
        case 'document':
            return in_array($extension, ALLOWED_DOCUMENT_TYPES);
        default:
            return false;
    }
}

?>
