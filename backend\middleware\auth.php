<?php
/**
 * وسطاء المصادقة
 * Authentication Middleware
 */

// منع الوصول المباشر
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

// متغير عام للمستخدم الحالي
$currentUser = null;

/**
 * التحقق من المصادقة
 */
function requireAuth() {
    global $currentUser;
    
    $token = getBearerToken();
    
    if (!$token) {
        errorResponse('Authentication required', 401);
    }
    
    $payload = validateJWT($token);
    
    if (!$payload) {
        errorResponse('Invalid or expired token', 401);
    }
    
    // التحقق من وجود المستخدم في قاعدة البيانات
    $user = getUserById($payload['user_id']);
    
    if (!$user) {
        errorResponse('User not found', 401);
    }
    
    if ($user['is_blocked']) {
        errorResponse('Account is blocked', 403);
    }
    
    // تحديث آخر نشاط
    updateUserLastActivity($user['id']);
    
    $currentUser = $user;
    return $user;
}

/**
 * التحقق من صلاحيات المشرف
 */
function requireAdminAuth() {
    $user = requireAuth();
    
    if ($user['account_type'] !== 'enterprise' && !isAdmin($user['id'])) {
        errorResponse('Admin access required', 403);
    }
    
    return $user;
}

/**
 * التحقق من صلاحيات VIP
 */
function requireVIPAuth() {
    $user = requireAuth();
    
    if (!in_array($user['account_type'], ['vip', 'enterprise'])) {
        errorResponse('VIP access required', 403);
    }
    
    return $user;
}

/**
 * الحصول على Bearer Token من الترويسة
 */
function getBearerToken() {
    $headers = getAuthorizationHeader();
    
    if (!$headers) {
        return null;
    }
    
    if (preg_match('/Bearer\s+(.*)$/i', $headers, $matches)) {
        return $matches[1];
    }
    
    return null;
}

/**
 * الحصول على ترويسة Authorization
 */
function getAuthorizationHeader() {
    $headers = null;
    
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER['Authorization']);
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
        
        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }
    
    return $headers;
}

/**
 * الحصول على المستخدم الحالي
 */
function getCurrentUser() {
    global $currentUser;
    return $currentUser;
}

/**
 * الحصول على معرف المستخدم الحالي
 */
function getCurrentUserId() {
    $user = getCurrentUser();
    return $user ? $user['id'] : null;
}

/**
 * الحصول على المستخدم بواسطة المعرف
 */
function getUserById($userId) {
    try {
        $db = getDB();
        
        $query = "SELECT * FROM users WHERE id = :id AND is_blocked = 0";
        $user = $db->selectOne($query, ['id' => $userId]);
        
        if ($user) {
            // فك تشفير الإعدادات
            $user['settings'] = $user['settings'] ? json_decode($user['settings'], true) : [];
            $user['privacy_settings'] = $user['privacy_settings'] ? json_decode($user['privacy_settings'], true) : [];
        }
        
        return $user;
        
    } catch (Exception $e) {
        error_log("Error getting user by ID: " . $e->getMessage());
        return null;
    }
}

/**
 * التحقق من كون المستخدم مشرف
 */
function isAdmin($userId) {
    try {
        $db = getDB();
        
        // يمكن إضافة جدول منفصل للمشرفين أو استخدام حقل في جدول المستخدمين
        $query = "SELECT COUNT(*) as count FROM users WHERE id = :id AND account_type = 'enterprise'";
        $result = $db->selectOne($query, ['id' => $userId]);
        
        return $result && $result['count'] > 0;
        
    } catch (Exception $e) {
        error_log("Error checking admin status: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث آخر نشاط للمستخدم
 */
function updateUserLastActivity($userId) {
    try {
        $db = getDB();
        
        $query = "UPDATE users SET last_seen = NOW(), is_online = 1 WHERE id = :id";
        $db->update($query, ['id' => $userId]);
        
        // تسجيل النشاط
        logActivity($userId, 'user_activity');
        
    } catch (Exception $e) {
        error_log("Error updating user activity: " . $e->getMessage());
    }
}

/**
 * تحديث حالة المستخدم (متصل/غير متصل)
 */
function updateUserOnlineStatus($userId, $isOnline = true) {
    try {
        $db = getDB();
        
        $query = "UPDATE users SET is_online = :is_online";
        $params = ['is_online' => $isOnline ? 1 : 0, 'id' => $userId];
        
        if (!$isOnline) {
            $query .= ", last_seen = NOW()";
        }
        
        $query .= " WHERE id = :id";
        
        return $db->update($query, $params);
        
    } catch (Exception $e) {
        error_log("Error updating online status: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من صلاحية الوصول للمحادثة
 */
function canAccessConversation($userId, $conversationId) {
    try {
        $db = getDB();
        
        $query = "SELECT COUNT(*) as count FROM conversation_participants 
                  WHERE user_id = :user_id AND conversation_id = :conversation_id AND left_at IS NULL";
        
        $result = $db->selectOne($query, [
            'user_id' => $userId,
            'conversation_id' => $conversationId
        ]);
        
        return $result && $result['count'] > 0;
        
    } catch (Exception $e) {
        error_log("Error checking conversation access: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من صلاحية الوصول للرسالة
 */
function canAccessMessage($userId, $messageId) {
    try {
        $db = getDB();
        
        $query = "SELECT m.conversation_id 
                  FROM messages m
                  JOIN conversation_participants cp ON m.conversation_id = cp.conversation_id
                  WHERE m.id = :message_id AND cp.user_id = :user_id AND cp.left_at IS NULL";
        
        $result = $db->selectOne($query, [
            'message_id' => $messageId,
            'user_id' => $userId
        ]);
        
        return $result !== false;
        
    } catch (Exception $e) {
        error_log("Error checking message access: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من حظر المستخدم
 */
function isUserBlocked($userId, $targetUserId) {
    try {
        $db = getDB();
        
        $query = "SELECT COUNT(*) as count FROM user_blocks 
                  WHERE (blocker_id = :user_id AND blocked_id = :target_id) 
                     OR (blocker_id = :target_id AND blocked_id = :user_id)";
        
        $result = $db->selectOne($query, [
            'user_id' => $userId,
            'target_id' => $targetUserId
        ]);
        
        return $result && $result['count'] > 0;
        
    } catch (Exception $e) {
        error_log("Error checking block status: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من صلاحية الميزة للمستخدم
 */
function canUseFeature($userId, $featureCode) {
    try {
        $user = getUserById($userId);
        
        if (!$user) {
            return false;
        }
        
        $db = getDB();
        
        // الحصول على إعدادات الميزة
        $query = "SELECT * FROM feature_flags WHERE feature_code = :feature_code AND is_active = 1";
        $feature = $db->selectOne($query, ['feature_code' => $featureCode]);
        
        if (!$feature) {
            return false;
        }
        
        // التحقق من الجمهور المستهدف
        switch ($feature['target_audience']) {
            case 'all':
                return true;
            case 'free':
                return $user['account_type'] === 'free';
            case 'vip':
                return in_array($user['account_type'], ['vip', 'enterprise']);
            case 'enterprise':
                return $user['account_type'] === 'enterprise';
            default:
                return false;
        }
        
    } catch (Exception $e) {
        error_log("Error checking feature access: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من حدود الاستخدام
 */
function checkUsageLimit($userId, $limitType) {
    try {
        $user = getUserById($userId);
        
        if (!$user) {
            return false;
        }
        
        // الحصول على حدود الخطة
        $planFeatures = getPlanFeatures($user['account_type']);
        
        if (!isset($planFeatures[$limitType])) {
            return true; // لا يوجد حد محدد
        }
        
        $limit = $planFeatures[$limitType];
        
        if ($limit === 0) {
            return true; // بلا حدود
        }
        
        // التحقق من الاستخدام الحالي
        $currentUsage = getCurrentUsage($userId, $limitType);
        
        return $currentUsage < $limit;
        
    } catch (Exception $e) {
        error_log("Error checking usage limit: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على ميزات الخطة
 */
function getPlanFeatures($accountType) {
    switch ($accountType) {
        case 'free':
            return FREE_PLAN_FEATURES;
        case 'vip':
            return VIP_PLAN_FEATURES;
        case 'enterprise':
            return ENTERPRISE_PLAN_FEATURES;
        default:
            return FREE_PLAN_FEATURES;
    }
}

/**
 * الحصول على الاستخدام الحالي
 */
function getCurrentUsage($userId, $limitType) {
    try {
        $db = getDB();
        
        switch ($limitType) {
            case 'max_groups':
                $query = "SELECT COUNT(*) as count FROM conversation_participants cp
                          JOIN conversations c ON cp.conversation_id = c.id
                          WHERE cp.user_id = :user_id AND c.type = 'group' AND cp.left_at IS NULL";
                break;
                
            case 'ai_requests_per_day':
                $query = "SELECT COUNT(*) as count FROM ai_interactions 
                          WHERE user_id = :user_id AND DATE(created_at) = CURDATE()";
                break;
                
            default:
                return 0;
        }
        
        $result = $db->selectOne($query, ['user_id' => $userId]);
        return $result ? (int)$result['count'] : 0;
        
    } catch (Exception $e) {
        error_log("Error getting current usage: " . $e->getMessage());
        return 0;
    }
}

?>
