# WhatsApp Clone - Git Ignore File

# Environment files
.env
.env.local
.env.production
.env.staging

# Composer
backend/vendor/
backend/composer.lock

# Logs
backend/logs/
*.log

# Cache
backend/cache/
backend/cache/*

# Uploads
backend/uploads/
backend/uploads/*
!backend/uploads/.gitkeep

# Backups
backend/backups/
backend/backups/*

# Node modules (if using any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Flutter
frontend/.dart_tool/
frontend/.flutter-plugins
frontend/.flutter-plugins-dependencies
frontend/.packages
frontend/.pub-cache/
frontend/.pub/
frontend/build/
frontend/ios/Flutter/Generated.xcconfig
frontend/ios/Flutter/flutter_export_environment.sh
frontend/ios/Runner/GeneratedPluginRegistrant.*
frontend/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
frontend/android/local.properties
frontend/android/key.properties

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Database
*.sql
*.sqlite
*.db

# Configuration files with sensitive data
backend/config/production.php
backend/config/local.php

# SSL certificates
*.pem
*.key
*.crt
*.csr

# API keys and secrets
secrets.json
keys.json

# Testing
backend/tests/coverage/
phpunit.xml

# Documentation build
docs/_build/

# Deployment scripts
deploy.sh
deploy.bat

# Local development
local/
dev/

# Compiled assets
public/css/
public/js/
public/mix-manifest.json

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test
