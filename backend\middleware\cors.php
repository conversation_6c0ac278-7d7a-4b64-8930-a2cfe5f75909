<?php
/**
 * وسطاء CORS
 * CORS Middleware
 */

// منع الوصول المباشر
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

/**
 * تعيين ترويسات CORS
 */
function setCorsHeaders() {
    // المصادر المسموحة
    $allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:8080',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:8080',
        APP_URL,
        ADMIN_URL
    ];
    
    // الحصول على المصدر الحالي
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    
    // التحقق من المصدر المسموح
    if (in_array($origin, $allowedOrigins) || DEBUG_MODE) {
        header("Access-Control-Allow-Origin: $origin");
    } else {
        header("Access-Control-Allow-Origin: " . APP_URL);
    }
    
    // ترويسات CORS الأساسية
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control, Pragma");
    header("Access-Control-Expose-Headers: Content-Length, X-JSON");
    header("Access-Control-Max-Age: 86400"); // 24 ساعة
    
    // ترويسات الأمان الإضافية
    header("X-Content-Type-Options: nosniff");
    header("X-Frame-Options: DENY");
    header("X-XSS-Protection: 1; mode=block");
    header("Referrer-Policy: strict-origin-when-cross-origin");
    
    // ترويسة Content Security Policy
    $csp = "default-src 'self'; " .
           "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " .
           "style-src 'self' 'unsafe-inline'; " .
           "img-src 'self' data: https:; " .
           "font-src 'self' data:; " .
           "connect-src 'self' ws: wss:; " .
           "media-src 'self' blob:; " .
           "object-src 'none'; " .
           "base-uri 'self'; " .
           "form-action 'self'";
    
    header("Content-Security-Policy: $csp");
}

/**
 * التحقق من المصدر المسموح
 */
function isAllowedOrigin($origin) {
    $allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:8080',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:8080',
        APP_URL,
        ADMIN_URL
    ];
    
    return in_array($origin, $allowedOrigins) || DEBUG_MODE;
}

/**
 * التحقق من الطريقة المسموحة
 */
function isAllowedMethod($method) {
    $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'];
    return in_array(strtoupper($method), $allowedMethods);
}

/**
 * معالجة طلب OPTIONS
 */
function handleOptionsRequest() {
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        setCorsHeaders();
        http_response_code(200);
        exit;
    }
}

?>
