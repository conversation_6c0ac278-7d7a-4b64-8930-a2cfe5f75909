# 🚀 دليل النشر - تطبيق الدردشة الاحترافي

## 📋 متطلبات الخادم

### الحد الأدنى:
- **نظام التشغيل:** Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **PHP:** 8.1 أو أحدث
- **MySQL:** 8.0 أو أحدث
- **Apache/Nginx:** أحدث إصدار مستقر
- **RAM:** 4GB (الحد الأدنى)
- **التخزين:** 50GB SSD
- **النطاق الترددي:** 100Mbps

### الموصى به:
- **RAM:** 8GB+
- **CPU:** 4 cores+
- **التخزين:** 100GB+ SSD
- **النطاق الترددي:** 1Gbps
- **CDN:** Cloudflare أو AWS CloudFront

## 🔧 إعداد الخادم

### 1. تثبيت PHP 8.1

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-json php8.1-curl php8.1-gd php8.1-mbstring php8.1-xml php8.1-zip php8.1-openssl
```

#### CentOS/RHEL:
```bash
sudo dnf install epel-release
sudo dnf install https://rpms.remirepo.net/enterprise/remi-release-8.rpm
sudo dnf module enable php:remi-8.1
sudo dnf install php php-fpm php-mysql php-json php-curl php-gd php-mbstring php-xml php-zip php-openssl
```

### 2. تثبيت MySQL 8.0

#### Ubuntu/Debian:
```bash
sudo apt install mysql-server-8.0
sudo mysql_secure_installation
```

#### CentOS/RHEL:
```bash
sudo dnf install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
sudo mysql_secure_installation
```

### 3. تثبيت Apache/Nginx

#### Apache:
```bash
sudo apt install apache2
sudo a2enmod rewrite
sudo a2enmod ssl
sudo systemctl restart apache2
```

#### Nginx:
```bash
sudo apt install nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 📁 نشر الملفات

### 1. رفع الملفات
```bash
# نسخ الملفات إلى الخادم
scp -r whatsapp/ user@server:/var/www/html/

# أو استخدام Git
git clone https://github.com/your-repo/whatsapp-clone.git /var/www/html/whatsapp
```

### 2. تعيين الصلاحيات
```bash
sudo chown -R www-data:www-data /var/www/html/whatsapp
sudo chmod -R 755 /var/www/html/whatsapp
sudo chmod -R 777 /var/www/html/whatsapp/backend/uploads
sudo chmod -R 777 /var/www/html/whatsapp/backend/logs
sudo chmod -R 777 /var/www/html/whatsapp/backend/cache
```

### 3. تثبيت Composer
```bash
cd /var/www/html/whatsapp/backend
curl -sS https://getcomposer.org/installer | php
php composer.phar install --no-dev --optimize-autoloader
```

## ⚙️ إعداد Apache

### Virtual Host Configuration:
```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /var/www/html/whatsapp
    
    <Directory /var/www/html/whatsapp>
        AllowOverride All
        Require all granted
    </Directory>
    
    # إعادة توجيه HTTP إلى HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/html/whatsapp
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    <Directory /var/www/html/whatsapp>
        AllowOverride All
        Require all granted
    </Directory>
    
    # إعدادات الأمان
    Header always set Strict-Transport-Security "max-age=********; includeSubDomains; preload"
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>
```

## 🔧 إعداد Nginx

### Server Block Configuration:
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    root /var/www/html/whatsapp;
    index index.php index.html;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # إعدادات الأمان
    add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload";
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    
    # معالجة PHP
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # API routing
    location /backend/api/ {
        try_files $uri $uri/ /backend/api/index.php?$query_string;
    }
    
    # منع الوصول للملفات الحساسة
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(log|sql|md)$ {
        deny all;
    }
    
    # تخزين مؤقت للملفات الثابتة
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🗄️ إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات والمستخدم
```sql
CREATE DATABASE whatsapp_clone CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'whatsapp_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON whatsapp_clone.* TO 'whatsapp_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. استيراد المخطط
```bash
mysql -u whatsapp_user -p whatsapp_clone < database/schema.sql
```

### 3. تحسين إعدادات MySQL
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
max_connections = 500
query_cache_size = 128M
query_cache_type = 1
```

## 🔐 إعداد SSL

### استخدام Let's Encrypt:
```bash
sudo apt install certbot python3-certbot-apache
sudo certbot --apache -d yourdomain.com
```

### أو Nginx:
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

## 🔧 إعداد التطبيق

### 1. نسخ ملف البيئة
```bash
cp backend/.env.example .env
```

### 2. تحديث إعدادات الإنتاج في .env
```env
APP_ENV=production
APP_DEBUG=false
DEBUG_MODE=false

DB_HOST=localhost
DB_NAME=whatsapp_clone
DB_USER=whatsapp_user
DB_PASS=your_strong_password

JWT_SECRET=your-super-secret-jwt-key-32-chars-minimum
ENCRYPTION_KEY=your-32-character-encryption-key
PASSWORD_SALT=your-unique-salt-change-this

# إعدادات الإنتاج
RATE_LIMIT_ENABLED=true
LOG_ENABLED=true
CACHE_ENABLED=true
```

### 3. تشغيل معالج الإعداد
```
https://yourdomain.com/setup.php
```

## 🔄 إعداد WebSocket

### 1. تثبيت Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. إعداد WebSocket Server
```javascript
// websocket-server.js
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', function connection(ws) {
    ws.on('message', function incoming(message) {
        // معالجة الرسائل الفورية
        wss.clients.forEach(function each(client) {
            if (client !== ws && client.readyState === WebSocket.OPEN) {
                client.send(message);
            }
        });
    });
});
```

### 3. تشغيل كخدمة
```bash
sudo npm install -g pm2
pm2 start websocket-server.js
pm2 startup
pm2 save
```

## 📊 مراقبة النظام

### 1. إعداد Logrotate
```bash
# /etc/logrotate.d/whatsapp
/var/www/html/whatsapp/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. مراقبة الأداء
```bash
# تثبيت htop
sudo apt install htop

# مراقبة MySQL
sudo apt install mytop

# مراقبة Apache/Nginx
sudo apt install apache2-utils  # للـ Apache
```

## 🔒 الأمان

### 1. جدار الحماية
```bash
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp  # WebSocket
```

### 2. تحديثات الأمان
```bash
# إعداد التحديثات التلقائية
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

### 3. مراقبة الأمان
```bash
# تثبيت fail2ban
sudo apt install fail2ban

# إعداد fail2ban للـ Apache
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
```

## 📱 نشر تطبيق Flutter

### 1. بناء التطبيق للإنتاج
```bash
cd frontend
flutter build apk --release
flutter build ios --release
```

### 2. رفع على متاجر التطبيقات
- **Google Play Store:** اتبع دليل Google Play Console
- **Apple App Store:** اتبع دليل App Store Connect

## 🔄 النسخ الاحتياطي التلقائي

### سكريبت النسخ الاحتياطي:
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/whatsapp"
DB_NAME="whatsapp_clone"
DB_USER="whatsapp_user"
DB_PASS="your_password"

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطية لقاعدة البيانات
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# نسخ احتياطية للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/html/whatsapp/backend/uploads

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### إضافة إلى Cron:
```bash
# تشغيل النسخ الاحتياطي يومياً في الساعة 2:00 صباحاً
0 2 * * * /path/to/backup.sh
```

## 🚀 تحسين الأداء

### 1. تفعيل OPcache
```ini
# /etc/php/8.1/fpm/conf.d/10-opcache.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
```

### 2. إعداد Redis للتخزين المؤقت
```bash
sudo apt install redis-server
sudo systemctl enable redis-server
```

### 3. ضغط Gzip
```apache
# Apache
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
</Location>
```

## 📞 الدعم والصيانة

### جهات الاتصال:
- **الدعم الفني:** <EMAIL>
- **الطوارئ:** +966500000000
- **الوثائق:** https://docs.whatsapp-clone.com

### جدولة الصيانة:
- **النسخ الاحتياطي:** يومياً الساعة 2:00 ص
- **التحديثات:** أسبوعياً يوم الأحد
- **مراجعة الأمان:** شهرياً

---
**آخر تحديث:** 2025-01-21
