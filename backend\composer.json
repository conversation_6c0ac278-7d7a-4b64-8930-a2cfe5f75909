{"name": "whatsapp-clone/backend", "description": "Backend API for WhatsApp Clone - Professional Chat Application", "type": "project", "license": "MIT", "authors": [{"name": "WhatsApp Clone Team", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "ext-pdo": "*", "ext-json": "*", "ext-openssl": "*", "ext-curl": "*", "ext-mbstring": "*", "ext-gd": "*", "firebase/php-jwt": "^6.8", "twilio/sdk": "^7.0", "stripe/stripe-php": "^10.0", "paypal/rest-api-sdk-php": "^1.14", "ratchet/pawl": "^0.4", "textalk/websocket-client": "^1.5", "intervention/image": "^2.7", "phpmailer/phpmailer": "^6.8", "monolog/monolog": "^3.4", "vlucas/phpdotenv": "^5.5", "ramsey/uuid": "^4.7"}, "require-dev": {"phpunit/phpunit": "^10.0", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^1.10"}, "autoload": {"psr-4": {"WhatsAppClone\\": "src/", "WhatsAppClone\\Controllers\\": "controllers/", "WhatsAppClone\\Models\\": "models/", "WhatsAppClone\\Middleware\\": "middleware/", "WhatsAppClone\\Utils\\": "utils/"}, "files": ["config/config.php", "config/database.php", "utils/helpers.php"]}, "autoload-dev": {"psr-4": {"WhatsAppClone\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyze": "phpstan analyse src/ --level=8", "post-install-cmd": ["@php -r \"if (!file_exists('.env')) { copy('.env.example', '.env'); }\""], "setup": ["composer install", "@php -r \"echo 'Setting up WhatsApp Clone Backend...\\n';\"", "@php setup/install.php"]}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}}