# 📚 وثائق API - تطبيق الدردشة الاحترافي

## 🌐 نظرة عامة

هذا دليل شامل لاستخدام API الخاص بتطبيق الدردشة الاحترافي. يوفر API جميع الوظائف المطلوبة لتطبيق دردشة متكامل.

## 🔗 URL الأساسي

```
http://localhost/whatsapp/backend/api
```

## 🔐 المصادقة

يستخدم API نظام JWT (JSON Web Tokens) للمصادقة. يجب تضمين الرمز المميز في ترويسة Authorization:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📋 نقاط النهاية الرئيسية

### 🔑 المصادقة (Authentication)

#### تسجيل مستخدم جديد
```http
POST /auth/register
Content-Type: application/json

{
    "phone": "+************",
    "name": "اسم المستخدم",
    "email": "<EMAIL>",
    "language": "ar"
}
```

**الاستجابة:**
```json
{
    "success": true,
    "status_code": 200,
    "message": "Verification code sent successfully",
    "data": {
        "phone": "+************",
        "otp_sent": true,
        "expires_in": 300
    }
}
```

#### التحقق من رمز OTP
```http
POST /auth/verify-otp
Content-Type: application/json

{
    "phone": "+************",
    "code": "123456",
    "type": "registration"
}
```

**الاستجابة:**
```json
{
    "success": true,
    "status_code": 200,
    "message": "Registration completed successfully",
    "data": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "Bearer",
        "expires_in": 86400,
        "user": {
            "id": 1,
            "phone": "+************",
            "name": "اسم المستخدم",
            "email": "<EMAIL>",
            "account_type": "free"
        }
    }
}
```

#### تسجيل الدخول
```http
POST /auth/login
Content-Type: application/json

{
    "phone": "+************"
}
```

#### تجديد الرمز المميز
```http
POST /auth/refresh
Content-Type: application/json

{
    "refresh_token": "YOUR_REFRESH_TOKEN"
}
```

#### تسجيل الخروج
```http
POST /auth/logout
Authorization: Bearer YOUR_JWT_TOKEN
```

### 👤 إدارة المستخدمين (Users)

#### الحصول على الملف الشخصي
```http
GET /users/profile
Authorization: Bearer YOUR_JWT_TOKEN
```

#### تحديث الملف الشخصي
```http
PUT /users/profile
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "name": "الاسم الجديد",
    "about": "حالة جديدة",
    "avatar": "base64_image_data"
}
```

#### البحث عن المستخدمين
```http
GET /users/search?q=search_term
Authorization: Bearer YOUR_JWT_TOKEN
```

#### حظر مستخدم
```http
POST /users/block
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "user_id": 123,
    "reason": "سبب الحظر"
}
```

#### إلغاء حظر مستخدم
```http
DELETE /users/block
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "user_id": 123
}
```

### 💬 الرسائل (Messages)

#### إرسال رسالة
```http
POST /messages/send
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "conversation_id": 1,
    "content": "نص الرسالة",
    "type": "text",
    "reply_to_id": null
}
```

#### الحصول على رسائل المحادثة
```http
GET /messages/conversation?conversation_id=1&page=1&limit=50
Authorization: Bearer YOUR_JWT_TOKEN
```

#### تحديد الرسائل كمقروءة
```http
PUT /messages/mark-read
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "message_ids": [1, 2, 3]
}
```

#### حذف رسالة
```http
DELETE /messages/delete
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "message_id": 1,
    "delete_for_everyone": false
}
```

### 🗣️ المحادثات (Conversations)

#### إنشاء محادثة جديدة
```http
POST /conversations
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "type": "private",
    "participants": [123],
    "name": "اسم المجموعة",
    "description": "وصف المجموعة"
}
```

#### الحصول على قائمة المحادثات
```http
GET /conversations?page=1&limit=20
Authorization: Bearer YOUR_JWT_TOKEN
```

#### الحصول على تفاصيل محادثة
```http
GET /conversations/123
Authorization: Bearer YOUR_JWT_TOKEN
```

### 📞 المكالمات (Calls)

#### بدء مكالمة
```http
POST /calls
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "conversation_id": 1,
    "type": "audio",
    "participants": [123, 456]
}
```

#### الحصول على تاريخ المكالمات
```http
GET /calls?page=1&limit=20
Authorization: Bearer YOUR_JWT_TOKEN
```

### 📁 الوسائط (Media)

#### رفع ملف
```http
POST /media/upload
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: multipart/form-data

file: [binary_file_data]
type: image|video|audio|document
```

### 📊 الحالات (Status)

#### نشر حالة جديدة
```http
POST /status
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "content": "نص الحالة",
    "media_url": "رابط الوسائط",
    "media_type": "text",
    "privacy": "contacts"
}
```

#### الحصول على الحالات
```http
GET /status
Authorization: Bearer YOUR_JWT_TOKEN
```

### 🤖 الذكاء الاصطناعي (AI)

#### طلب مساعدة من الذكاء الاصطناعي
```http
POST /ai/assist
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
    "type": "summary|reply|translation|analysis",
    "input": "النص المراد معالجته",
    "conversation_id": 1
}
```

### ⚙️ الميزات (Features)

#### الحصول على قائمة الميزات المتاحة
```http
GET /features/list
```

#### التحقق من ميزة معينة
```http
POST /features/check
Content-Type: application/json

{
    "feature_code": "ai_assistant",
    "user_id": 123
}
```

## 📝 رموز الاستجابة

| الرمز | المعنى |
|-------|---------|
| 200 | نجح الطلب |
| 201 | تم الإنشاء بنجاح |
| 400 | خطأ في البيانات المرسلة |
| 401 | غير مصرح |
| 403 | ممنوع |
| 404 | غير موجود |
| 409 | تعارض |
| 429 | تجاوز حد المعدل |
| 500 | خطأ في الخادم |

## 🔒 الأمان

### Rate Limiting
- **عام:** 100 طلب في الساعة لكل IP
- **المصادقة:** 5 محاولات كل 15 دقيقة
- **الرسائل:** 100 رسالة في الساعة لكل مستخدم
- **المكالمات:** 20 مكالمة في الساعة لكل مستخدم

### التشفير
- جميع الرسائل مشفرة End-to-End
- كلمات المرور محمية بـ Argon2ID
- الرموز المميزة محمية بـ HMAC SHA-256

## 🧪 اختبار API

يمكنك اختبار API باستخدام:

### cURL
```bash
curl -X POST http://localhost/whatsapp/backend/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"phone":"+************","name":"اسم المستخدم"}'
```

### Postman
استيراد مجموعة Postman من: `docs/api/postman_collection.json`

### JavaScript
```javascript
const response = await fetch('/backend/api/auth/register', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        phone: '+************',
        name: 'اسم المستخدم'
    })
});

const data = await response.json();
console.log(data);
```

## 🐛 معالجة الأخطاء

جميع الاستجابات تتبع نفس التنسيق:

```json
{
    "success": false,
    "status_code": 400,
    "message": "رسالة الخطأ",
    "errors": {
        "field_name": ["رسالة خطأ محددة"]
    }
}
```

## 📞 الدعم

للحصول على المساعدة:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +************
- 💬 الدردشة المباشرة: متاحة في لوحة التحكم

---
**آخر تحديث:** 2025-01-21
