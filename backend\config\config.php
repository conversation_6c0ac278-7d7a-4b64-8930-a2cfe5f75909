<?php
/**
 * ملف التكوين الرئيسي لتطبيق الدردشة
 * WhatsApp Clone Configuration File
 */

// منع الوصول المباشر للملف
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'whatsapp_clone');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات التطبيق العامة
define('APP_NAME', 'WhatsApp Clone');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/whatsapp');
define('API_URL', APP_URL . '/backend/api');
define('ADMIN_URL', APP_URL . '/admin');

// إعدادات الأمان
define('JWT_SECRET', 'your-super-secret-jwt-key-change-this-in-production');
define('JWT_EXPIRE', 86400); // 24 ساعة
define('ENCRYPTION_KEY', 'your-32-character-encryption-key-here');
define('PASSWORD_SALT', 'your-unique-salt-here');

// إعدادات الملفات والرفع
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv']);
define('ALLOWED_AUDIO_TYPES', ['mp3', 'wav', 'ogg', 'm4a']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx']);

// إعدادات الرسائل
define('MAX_MESSAGE_LENGTH', 4096);
define('MAX_GROUP_MEMBERS', 256);
define('MESSAGE_ENCRYPTION', true);

// إعدادات OTP والتحقق
define('OTP_LENGTH', 6);
define('OTP_EXPIRE', 300); // 5 دقائق
define('SMS_PROVIDER', 'twilio'); // twilio, nexmo, local
define('TWILIO_SID', 'your-twilio-sid');
define('TWILIO_TOKEN', 'your-twilio-token');
define('TWILIO_PHONE', 'your-twilio-phone-number');

// إعدادات البريد الإلكتروني
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-email-password');
define('MAIL_FROM_NAME', APP_NAME);

// إعدادات WebSocket للرسائل الفورية
define('WEBSOCKET_HOST', 'localhost');
define('WEBSOCKET_PORT', 8080);
define('WEBSOCKET_ENABLED', true);

// إعدادات الذكاء الاصطناعي
define('AI_ENABLED', true);
define('OPENAI_API_KEY', 'your-openai-api-key');
define('AI_MODEL', 'gpt-3.5-turbo');
define('AI_MAX_TOKENS', 150);

// إعدادات المكالمات
define('WEBRTC_ENABLED', true);
define('STUN_SERVERS', [
    'stun:stun.l.google.com:19302',
    'stun:stun1.l.google.com:19302'
]);

// إعدادات الدفع
define('STRIPE_ENABLED', true);
define('STRIPE_PUBLIC_KEY', 'pk_test_your_stripe_public_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('PAYPAL_ENABLED', true);
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');

// إعدادات الاشتراكات
define('FREE_PLAN_FEATURES', [
    'max_groups' => 5,
    'max_file_size' => 10, // MB
    'ai_requests_per_day' => 10,
    'call_duration_limit' => 30 // دقيقة
]);

define('VIP_PLAN_FEATURES', [
    'max_groups' => 50,
    'max_file_size' => 100, // MB
    'ai_requests_per_day' => 100,
    'call_duration_limit' => 0, // بلا حدود
    'priority_support' => true
]);

define('ENTERPRISE_PLAN_FEATURES', [
    'max_groups' => 0, // بلا حدود
    'max_file_size' => 500, // MB
    'ai_requests_per_day' => 0, // بلا حدود
    'call_duration_limit' => 0, // بلا حدود
    'priority_support' => true,
    'custom_branding' => true,
    'advanced_analytics' => true
]);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DRIVER', 'file'); // file, redis, memcached
define('CACHE_TTL', 3600); // ساعة واحدة

// إعدادات السجلات
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'info'); // debug, info, warning, error
define('LOG_PATH', __DIR__ . '/../logs/');

// إعدادات التطوير
define('DEBUG_MODE', true);
define('ERROR_REPORTING', true);
define('SHOW_ERRORS', true);

// إعدادات الأمان المتقدمة
define('RATE_LIMIT_ENABLED', true);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('CSRF_PROTECTION', true);
define('XSS_PROTECTION', true);

// إعدادات Feature Flags
define('FEATURE_FLAGS_ENABLED', true);
define('DEFAULT_FEATURES', [
    'messaging' => true,
    'voice_calls' => true,
    'video_calls' => true,
    'groups' => true,
    'ai_assistant' => false,
    'file_sharing' => true,
    'status_updates' => true,
    'disappearing_messages' => false,
    'scheduled_messages' => false,
    'message_reactions' => true,
    'dark_mode' => true,
    'backup_restore' => true
]);

// إعدادات الإشعارات
define('PUSH_NOTIFICATIONS_ENABLED', true);
define('FCM_SERVER_KEY', 'your-fcm-server-key');
define('APNS_CERTIFICATE', 'path-to-apns-certificate');

// إعدادات التحليلات
define('ANALYTICS_ENABLED', true);
define('GOOGLE_ANALYTICS_ID', 'GA-XXXXXXXXX');

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', true);
define('BACKUP_INTERVAL', 86400); // يومياً
define('BACKUP_RETENTION', 30); // 30 يوم

// إعدادات المنطقة الزمنية واللغة
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en', 'fr']);

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات PHP
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);

?>
