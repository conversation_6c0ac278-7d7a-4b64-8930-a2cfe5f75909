<?php
/**
 * متحكم المصادقة
 * Authentication Controller
 */

// منع الوصول المباشر
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

class AuthController {
    private $db;
    private $rateLimiter;
    
    public function __construct() {
        $this->db = getDB();
        $this->rateLimiter = new RateLimiter();
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        try {
            // التحقق من البيانات المطلوبة
            $requiredFields = ['phone', 'name'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    errorResponse("Field {$field} is required", 400);
                }
            }
            
            // تنظيف وتحقق من رقم الهاتف
            $phone = cleanPhone($data['phone']);
            if (!isValidPhone($phone)) {
                errorResponse('Invalid phone number format', 400);
            }
            
            // التحقق من البريد الإلكتروني إذا تم توفيره
            if (!empty($data['email']) && !isValidEmail($data['email'])) {
                errorResponse('Invalid email format', 400);
            }
            
            // التحقق من حد المعدل
            $clientIP = getRealIP();
            if (!$this->rateLimiter->checkAuthLimit($clientIP)) {
                errorResponse('Too many registration attempts. Please try again later.', 429);
            }
            
            // التحقق من وجود المستخدم
            if ($this->userExists($phone, $data['email'] ?? null)) {
                errorResponse('User already exists', 409);
            }
            
            // إنشاء وإرسال OTP
            $otp = $this->generateAndSendOTP($phone, 'registration');
            
            if (!$otp) {
                errorResponse('Failed to send verification code', 500);
            }
            
            // حفظ بيانات التسجيل مؤقتاً
            $tempData = [
                'phone' => $phone,
                'email' => $data['email'] ?? null,
                'name' => sanitizeInput($data['name']),
                'language' => $data['language'] ?? DEFAULT_LANGUAGE
            ];
            
            setCache("registration_{$phone}", $tempData, 600); // 10 دقائق
            
            successResponse([
                'phone' => $phone,
                'otp_sent' => true,
                'expires_in' => OTP_EXPIRE
            ], 'Verification code sent successfully');
            
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            errorResponse('Registration failed', 500);
        }
    }
    
    /**
     * التحقق من رمز OTP
     */
    public function verifyOTP($data) {
        try {
            // التحقق من البيانات المطلوبة
            if (empty($data['phone']) || empty($data['code'])) {
                errorResponse('Phone and verification code are required', 400);
            }
            
            $phone = cleanPhone($data['phone']);
            $code = sanitizeInput($data['code']);
            
            // التحقق من حد المعدل
            if (!$this->rateLimiter->checkAuthLimit($phone)) {
                errorResponse('Too many verification attempts. Please try again later.', 429);
            }
            
            // التحقق من صحة OTP
            if (!$this->verifyOTPCode($phone, $code, $data['type'] ?? 'registration')) {
                errorResponse('Invalid or expired verification code', 400);
            }
            
            // معالجة حسب نوع التحقق
            switch ($data['type'] ?? 'registration') {
                case 'registration':
                    return $this->completeRegistration($phone);
                    
                case 'login':
                    return $this->completeLogin($phone);
                    
                case 'password_reset':
                    return $this->allowPasswordReset($phone);
                    
                default:
                    errorResponse('Invalid verification type', 400);
            }
            
        } catch (Exception $e) {
            error_log("OTP verification error: " . $e->getMessage());
            errorResponse('Verification failed', 500);
        }
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($data) {
        try {
            // التحقق من البيانات المطلوبة
            if (empty($data['phone'])) {
                errorResponse('Phone number is required', 400);
            }
            
            $phone = cleanPhone($data['phone']);
            
            // التحقق من حد المعدل
            if (!$this->rateLimiter->checkAuthLimit($phone)) {
                errorResponse('Too many login attempts. Please try again later.', 429);
            }
            
            // التحقق من وجود المستخدم
            $user = $this->getUserByPhone($phone);
            if (!$user) {
                errorResponse('User not found', 404);
            }
            
            if ($user['is_blocked']) {
                errorResponse('Account is blocked', 403);
            }
            
            // إرسال OTP للتحقق
            $otp = $this->generateAndSendOTP($phone, 'login');
            
            if (!$otp) {
                errorResponse('Failed to send verification code', 500);
            }
            
            successResponse([
                'phone' => $phone,
                'otp_sent' => true,
                'expires_in' => OTP_EXPIRE
            ], 'Verification code sent successfully');
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            errorResponse('Login failed', 500);
        }
    }
    
    /**
     * تجديد الرمز المميز
     */
    public function refreshToken($data) {
        try {
            if (empty($data['refresh_token'])) {
                errorResponse('Refresh token is required', 400);
            }
            
            // التحقق من صحة الرمز المميز
            $payload = validateJWT($data['refresh_token']);
            
            if (!$payload || $payload['type'] !== 'refresh') {
                errorResponse('Invalid refresh token', 401);
            }
            
            // التحقق من وجود المستخدم
            $user = getUserById($payload['user_id']);
            if (!$user) {
                errorResponse('User not found', 404);
            }
            
            // إنشاء رموز جديدة
            $tokens = $this->generateTokens($user);
            
            // تحديث آخر نشاط
            updateUserLastActivity($user['id']);
            
            successResponse($tokens, 'Token refreshed successfully');
            
        } catch (Exception $e) {
            error_log("Token refresh error: " . $e->getMessage());
            errorResponse('Token refresh failed', 500);
        }
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        try {
            $user = getCurrentUser();
            
            if ($user) {
                // تحديث حالة المستخدم
                updateUserOnlineStatus($user['id'], false);
                
                // تسجيل النشاط
                logActivity($user['id'], 'logout');
            }
            
            successResponse(null, 'Logged out successfully');
            
        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            errorResponse('Logout failed', 500);
        }
    }
    
    /**
     * نسيان كلمة المرور
     */
    public function forgotPassword($data) {
        try {
            if (empty($data['phone'])) {
                errorResponse('Phone number is required', 400);
            }
            
            $phone = cleanPhone($data['phone']);
            
            // التحقق من حد المعدل
            if (!$this->rateLimiter->checkAuthLimit($phone)) {
                errorResponse('Too many password reset attempts. Please try again later.', 429);
            }
            
            // التحقق من وجود المستخدم
            $user = $this->getUserByPhone($phone);
            if (!$user) {
                // لا نكشف عن عدم وجود المستخدم لأسباب أمنية
                successResponse([
                    'phone' => $phone,
                    'otp_sent' => true
                ], 'If this phone number is registered, you will receive a verification code');
                return;
            }
            
            // إرسال OTP
            $otp = $this->generateAndSendOTP($phone, 'password_reset');
            
            successResponse([
                'phone' => $phone,
                'otp_sent' => true,
                'expires_in' => OTP_EXPIRE
            ], 'Password reset code sent successfully');
            
        } catch (Exception $e) {
            error_log("Forgot password error: " . $e->getMessage());
            errorResponse('Password reset failed', 500);
        }
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword($data) {
        try {
            $requiredFields = ['phone', 'code', 'new_password'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    errorResponse("Field {$field} is required", 400);
                }
            }
            
            $phone = cleanPhone($data['phone']);
            
            // التحقق من OTP
            if (!$this->verifyOTPCode($phone, $data['code'], 'password_reset')) {
                errorResponse('Invalid or expired verification code', 400);
            }
            
            // التحقق من قوة كلمة المرور
            if (strlen($data['new_password']) < 6) {
                errorResponse('Password must be at least 6 characters long', 400);
            }
            
            // تحديث كلمة المرور
            $hashedPassword = hashPassword($data['new_password']);
            
            $query = "UPDATE users SET password = :password WHERE phone = :phone";
            $result = $this->db->update($query, [
                'password' => $hashedPassword,
                'phone' => $phone
            ]);
            
            if ($result) {
                // تسجيل النشاط
                $user = $this->getUserByPhone($phone);
                if ($user) {
                    logActivity($user['id'], 'password_reset');
                }
                
                successResponse(null, 'Password reset successfully');
            } else {
                errorResponse('Failed to reset password', 500);
            }
            
        } catch (Exception $e) {
            error_log("Reset password error: " . $e->getMessage());
            errorResponse('Password reset failed', 500);
        }
    }
    
    /**
     * التحقق من وجود المستخدم
     */
    private function userExists($phone, $email = null) {
        $query = "SELECT COUNT(*) as count FROM users WHERE phone = :phone";
        $params = ['phone' => $phone];
        
        if ($email) {
            $query .= " OR email = :email";
            $params['email'] = $email;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result && $result['count'] > 0;
    }
    
    /**
     * الحصول على المستخدم بواسطة رقم الهاتف
     */
    private function getUserByPhone($phone) {
        $query = "SELECT * FROM users WHERE phone = :phone";
        return $this->db->selectOne($query, ['phone' => $phone]);
    }
    
    /**
     * إنشاء وإرسال OTP
     */
    private function generateAndSendOTP($phone, $type = 'registration') {
        try {
            $otp = generateOTP(OTP_LENGTH);
            $expiresAt = date('Y-m-d H:i:s', time() + OTP_EXPIRE);
            
            // حفظ OTP في قاعدة البيانات
            $query = "INSERT INTO otp_verifications (phone, code, type, expires_at) 
                      VALUES (:phone, :code, :type, :expires_at)
                      ON DUPLICATE KEY UPDATE 
                      code = :code, attempts = 0, is_verified = 0, expires_at = :expires_at";
            
            $result = $this->db->insert($query, [
                'phone' => $phone,
                'code' => $otp,
                'type' => $type,
                'expires_at' => $expiresAt
            ]);
            
            if ($result) {
                // إرسال SMS (يمكن تطبيق الإرسال الفعلي هنا)
                $this->sendSMS($phone, $otp);
                return $otp;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("OTP generation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من رمز OTP
     */
    private function verifyOTPCode($phone, $code, $type) {
        try {
            $query = "SELECT * FROM otp_verifications 
                      WHERE phone = :phone AND code = :code AND type = :type 
                      AND expires_at > NOW() AND is_verified = 0 AND attempts < 3";
            
            $otp = $this->db->selectOne($query, [
                'phone' => $phone,
                'code' => $code,
                'type' => $type
            ]);
            
            if ($otp) {
                // تحديث حالة التحقق
                $updateQuery = "UPDATE otp_verifications 
                                SET is_verified = 1, attempts = attempts + 1 
                                WHERE id = :id";
                
                $this->db->update($updateQuery, ['id' => $otp['id']]);
                return true;
            } else {
                // زيادة عدد المحاولات
                $updateQuery = "UPDATE otp_verifications 
                                SET attempts = attempts + 1 
                                WHERE phone = :phone AND type = :type AND expires_at > NOW()";
                
                $this->db->update($updateQuery, ['phone' => $phone, 'type' => $type]);
                return false;
            }
            
        } catch (Exception $e) {
            error_log("OTP verification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إكمال التسجيل
     */
    private function completeRegistration($phone) {
        try {
            // الحصول على البيانات المؤقتة
            $tempData = getFromCache("registration_{$phone}");
            
            if (!$tempData) {
                errorResponse('Registration data not found', 400);
            }
            
            // إنشاء المستخدم
            $userData = [
                'phone' => $phone,
                'email' => $tempData['email'],
                'name' => $tempData['name'],
                'language' => $tempData['language'],
                'settings' => json_encode([
                    'notifications' => true,
                    'privacy' => [
                        'last_seen' => 'everyone',
                        'profile_photo' => 'everyone',
                        'about' => 'everyone'
                    ]
                ])
            ];
            
            $query = "INSERT INTO users (phone, email, name, language, settings) 
                      VALUES (:phone, :email, :name, :language, :settings)";
            
            $userId = $this->db->insert($query, $userData);
            
            if ($userId) {
                // إنشاء الرموز المميزة
                $user = getUserById($userId);
                $tokens = $this->generateTokens($user);
                
                // تسجيل النشاط
                logActivity($userId, 'user_registered');
                
                // مسح البيانات المؤقتة
                setCache("registration_{$phone}", null, 0);
                
                successResponse(array_merge($tokens, [
                    'user' => $this->formatUserData($user)
                ]), 'Registration completed successfully');
            } else {
                errorResponse('Failed to create user', 500);
            }
            
        } catch (Exception $e) {
            error_log("Complete registration error: " . $e->getMessage());
            errorResponse('Registration completion failed', 500);
        }
    }
    
    /**
     * إكمال تسجيل الدخول
     */
    private function completeLogin($phone) {
        try {
            $user = $this->getUserByPhone($phone);
            
            if (!$user) {
                errorResponse('User not found', 404);
            }
            
            // إنشاء الرموز المميزة
            $tokens = $this->generateTokens($user);
            
            // تحديث آخر نشاط
            updateUserLastActivity($user['id']);
            
            // تسجيل النشاط
            logActivity($user['id'], 'user_login');
            
            successResponse(array_merge($tokens, [
                'user' => $this->formatUserData($user)
            ]), 'Login successful');
            
        } catch (Exception $e) {
            error_log("Complete login error: " . $e->getMessage());
            errorResponse('Login completion failed', 500);
        }
    }
    
    /**
     * إنشاء الرموز المميزة
     */
    private function generateTokens($user) {
        $accessPayload = [
            'user_id' => $user['id'],
            'phone' => $user['phone'],
            'account_type' => $user['account_type'],
            'type' => 'access'
        ];
        
        $refreshPayload = [
            'user_id' => $user['id'],
            'type' => 'refresh'
        ];
        
        return [
            'access_token' => createJWT($accessPayload),
            'refresh_token' => createJWT($refreshPayload),
            'token_type' => 'Bearer',
            'expires_in' => JWT_EXPIRE
        ];
    }
    
    /**
     * تنسيق بيانات المستخدم
     */
    private function formatUserData($user) {
        return [
            'id' => $user['id'],
            'phone' => $user['phone'],
            'email' => $user['email'],
            'name' => $user['name'],
            'avatar' => $user['avatar'],
            'status' => $user['status'],
            'about' => $user['about'],
            'account_type' => $user['account_type'],
            'is_verified' => (bool)$user['is_verified'],
            'language' => $user['language'],
            'created_at' => $user['created_at']
        ];
    }
    
    /**
     * إرسال SMS
     */
    private function sendSMS($phone, $otp) {
        // هنا يمكن تطبيق إرسال SMS الفعلي باستخدام Twilio أو أي خدمة أخرى
        // للتطوير، سنطبع الرمز في السجل
        error_log("SMS OTP for {$phone}: {$otp}");
        
        // في الإنتاج، استخدم خدمة SMS حقيقية
        return true;
    }
    
    /**
     * السماح بإعادة تعيين كلمة المرور
     */
    private function allowPasswordReset($phone) {
        successResponse([
            'phone' => $phone,
            'reset_allowed' => true
        ], 'You can now reset your password');
    }
}

?>
