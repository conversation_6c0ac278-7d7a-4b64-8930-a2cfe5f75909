# 🗄️ وثائق قاعدة البيانات - تطبيق الدردشة الاحترافي

## 📋 نظرة عامة

قاعدة البيانات مصممة لدعم تطبيق دردشة شامل مع ميزات متقدمة مثل الذكاء الاصطناعي، المكالمات، والاشتراكات المدفوعة.

## 🏗️ مخطط قاعدة البيانات

### 👤 جدول المستخدمين (users)

```sql
CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) NOT NULL UNIQUE,
  `email` varchar(255) DEFAULT NULL UNIQUE,
  `name` varchar(100) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `status` text DEFAULT NULL,
  `about` varchar(255) DEFAULT 'Hey there! I am using WhatsApp Clone.',
  `account_type` enum('free','vip','enterprise') DEFAULT 'free',
  `is_verified` tinyint(1) DEFAULT 0,
  `is_blocked` tinyint(1) DEFAULT 0,
  `is_online` tinyint(1) DEFAULT 0,
  `last_seen` timestamp NULL DEFAULT NULL,
  `language` varchar(5) DEFAULT 'ar',
  `timezone` varchar(50) DEFAULT 'Asia/Riyadh',
  `settings` json DEFAULT NULL,
  `privacy_settings` json DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

**الحقول الرئيسية:**
- `phone`: رقم الهاتف (مفتاح فريد)
- `account_type`: نوع الحساب (مجاني، VIP، Enterprise)
- `settings`: إعدادات المستخدم بصيغة JSON
- `privacy_settings`: إعدادات الخصوصية

### 📱 جدول أجهزة المستخدمين (user_devices)

```sql
CREATE TABLE `user_devices` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `device_name` varchar(100) DEFAULT NULL,
  `device_type` enum('android','ios','web') NOT NULL,
  `fcm_token` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
);
```

### 💬 جدول المحادثات (conversations)

```sql
CREATE TABLE `conversations` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('private','group') NOT NULL DEFAULT 'private',
  `name` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `settings` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 👥 جدول أعضاء المحادثات (conversation_participants)

```sql
CREATE TABLE `conversation_participants` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `role` enum('member','admin','owner') DEFAULT 'member',
  `joined_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `left_at` timestamp NULL DEFAULT NULL,
  `is_muted` tinyint(1) DEFAULT 0,
  `is_pinned` tinyint(1) DEFAULT 0,
  `last_read_message_id` bigint(20) UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### 📝 جدول الرسائل (messages)

```sql
CREATE TABLE `messages` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) UNSIGNED NOT NULL,
  `sender_id` bigint(20) UNSIGNED NOT NULL,
  `reply_to_id` bigint(20) UNSIGNED DEFAULT NULL,
  `forward_from_id` bigint(20) UNSIGNED DEFAULT NULL,
  `content` text DEFAULT NULL,
  `content_encrypted` text DEFAULT NULL,
  `type` enum('text','image','video','audio','document','location','contact','sticker','voice_note') DEFAULT 'text',
  `metadata` json DEFAULT NULL,
  `status` enum('sent','delivered','read','deleted') DEFAULT 'sent',
  `is_edited` tinyint(1) DEFAULT 0,
  `is_deleted_for_everyone` tinyint(1) DEFAULT 0,
  `expires_at` timestamp NULL DEFAULT NULL,
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

**أنواع الرسائل:**
- `text`: رسالة نصية
- `image`: صورة
- `video`: فيديو
- `audio`: ملف صوتي
- `document`: مستند
- `location`: موقع
- `contact`: جهة اتصال
- `sticker`: ملصق
- `voice_note`: رسالة صوتية

### 📁 جدول الوسائط (media)

```sql
CREATE TABLE `media` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) UNSIGNED NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `size` bigint(20) UNSIGNED NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `dimensions` varchar(20) DEFAULT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `url` varchar(500) NOT NULL,
  `is_encrypted` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 📞 جدول المكالمات (calls)

```sql
CREATE TABLE `calls` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `conversation_id` bigint(20) UNSIGNED DEFAULT NULL,
  `caller_id` bigint(20) UNSIGNED NOT NULL,
  `type` enum('audio','video') NOT NULL,
  `status` enum('initiated','ringing','answered','ended','missed','declined','failed') DEFAULT 'initiated',
  `duration` int(11) DEFAULT 0,
  `quality_rating` tinyint(1) DEFAULT NULL,
  `participants` json DEFAULT NULL,
  `started_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `answered_at` timestamp NULL DEFAULT NULL,
  `ended_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### 📊 جدول الحالات (status_updates)

```sql
CREATE TABLE `status_updates` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `content` text DEFAULT NULL,
  `media_url` varchar(500) DEFAULT NULL,
  `media_type` enum('text','image','video') DEFAULT 'text',
  `background_color` varchar(7) DEFAULT NULL,
  `font_style` varchar(50) DEFAULT NULL,
  `privacy` enum('all','contacts','selected','except') DEFAULT 'contacts',
  `viewers` json DEFAULT NULL,
  `view_count` int(11) DEFAULT 0,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### ⚙️ جدول Feature Flags (feature_flags)

```sql
CREATE TABLE `feature_flags` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `feature_code` varchar(100) NOT NULL UNIQUE,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `target_audience` enum('all','free','vip','enterprise','beta_users') DEFAULT 'all',
  `rollout_percentage` tinyint(3) UNSIGNED DEFAULT 100,
  `conditions` json DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 💳 جدول الاشتراكات (subscriptions)

```sql
CREATE TABLE `subscriptions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `plan` enum('free','vip','enterprise') NOT NULL DEFAULT 'free',
  `status` enum('active','cancelled','expired','pending') DEFAULT 'active',
  `payment_method` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT 0.00,
  `currency` varchar(3) DEFAULT 'USD',
  `billing_cycle` enum('monthly','yearly') DEFAULT 'monthly',
  `start_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `end_date` timestamp NULL DEFAULT NULL,
  `auto_renew` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`)
);
```

### 🤖 جدول الذكاء الاصطناعي (ai_interactions)

```sql
CREATE TABLE `ai_interactions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `conversation_id` bigint(20) UNSIGNED DEFAULT NULL,
  `request_type` enum('summary','reply','translation','analysis','suggestion') NOT NULL,
  `input_text` text NOT NULL,
  `output_text` text DEFAULT NULL,
  `model_used` varchar(50) DEFAULT 'gpt-3.5-turbo',
  `tokens_used` int(11) DEFAULT 0,
  `processing_time` decimal(5,3) DEFAULT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'pending',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 🔒 جدول الحظر (user_blocks)

```sql
CREATE TABLE `user_blocks` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `blocker_id` bigint(20) UNSIGNED NOT NULL,
  `blocked_id` bigint(20) UNSIGNED NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 📋 جدول التقارير (reports)

```sql
CREATE TABLE `reports` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `reporter_id` bigint(20) UNSIGNED NOT NULL,
  `reported_user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `reported_message_id` bigint(20) UNSIGNED DEFAULT NULL,
  `type` enum('spam','harassment','inappropriate_content','fake_account','other') NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('pending','reviewed','resolved','dismissed') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## 🔗 العلاقات

### العلاقات الرئيسية:
1. **المستخدم ↔ المحادثات**: علاقة many-to-many عبر `conversation_participants`
2. **المحادثة ↔ الرسائل**: علاقة one-to-many
3. **الرسالة ↔ الوسائط**: علاقة one-to-many
4. **المستخدم ↔ الاشتراكات**: علاقة one-to-many
5. **المستخدم ↔ الحالات**: علاقة one-to-many

## 📊 الفهارس

### الفهارس المهمة للأداء:
```sql
-- فهارس المستخدمين
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_account_type ON users(account_type);
CREATE INDEX idx_users_online ON users(is_online);

-- فهارس الرسائل
CREATE INDEX idx_messages_conversation_time ON messages(conversation_id, sent_at);
CREATE INDEX idx_messages_sender ON messages(sender_id);
CREATE INDEX idx_messages_status ON messages(status);

-- فهارس المحادثات
CREATE INDEX idx_conversation_participants ON conversation_participants(conversation_id, user_id);

-- فهارس المكالمات
CREATE INDEX idx_calls_caller_time ON calls(caller_id, started_at);
CREATE INDEX idx_calls_status ON calls(status);
```

## 🔄 النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية:
```bash
mysqldump -u username -p whatsapp_clone > backup_$(date +%Y%m%d_%H%M%S).sql
```

### استعادة النسخة الاحتياطية:
```bash
mysql -u username -p whatsapp_clone < backup_file.sql
```

## 🔧 الصيانة

### تنظيف البيانات القديمة:
```sql
-- حذف الرسائل المؤقتة المنتهية الصلاحية
DELETE FROM messages WHERE expires_at IS NOT NULL AND expires_at < NOW();

-- حذف الحالات المنتهية الصلاحية
DELETE FROM status_updates WHERE expires_at < NOW();

-- حذف رموز OTP المنتهية الصلاحية
DELETE FROM otp_verifications WHERE expires_at < NOW();
```

### تحسين الجداول:
```sql
OPTIMIZE TABLE users, messages, conversations, calls;
```

## 📈 مراقبة الأداء

### استعلامات مراقبة الأداء:
```sql
-- حجم قاعدة البيانات
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'whatsapp_clone';

-- أكثر الجداول استخداماً
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'whatsapp_clone'
ORDER BY (data_length + index_length) DESC;

-- الاستعلامات البطيئة
SHOW PROCESSLIST;
```

## 🛡️ الأمان

### إعدادات الأمان:
- جميع كلمات المرور مشفرة بـ Argon2ID
- الرسائل الحساسة مشفرة في قاعدة البيانات
- استخدام Prepared Statements لمنع SQL Injection
- تحديد صلاحيات المستخدمين بدقة

### نصائح الأمان:
1. استخدم مستخدم قاعدة بيانات منفصل للتطبيق
2. قم بتشفير النسخ الاحتياطية
3. راقب الوصول غير المصرح به
4. قم بتحديث MySQL بانتظام

---
**آخر تحديث:** 2025-01-21
