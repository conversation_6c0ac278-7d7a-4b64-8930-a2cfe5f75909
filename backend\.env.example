# WhatsApp Clone Backend Environment Configuration
# نسخ هذا الملف إلى .env وتحديث القيم

# إعدادات التطبيق العامة
APP_NAME="WhatsApp Clone"
APP_VERSION="1.0.0"
APP_URL="http://localhost/whatsapp"
APP_ENV="development"
APP_DEBUG=true

# إعدادات قاعدة البيانات
DB_HOST="localhost"
DB_NAME="whatsapp_clone"
DB_USER="root"
DB_PASS=""
DB_CHARSET="utf8mb4"
DB_PORT=3306

# إعدادات الأمان
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production-32-chars-min"
JWT_EXPIRE=86400
ENCRYPTION_KEY="your-32-character-encryption-key-here"
PASSWORD_SALT="your-unique-salt-here-change-this"

# إعدادات الملفات والرفع
MAX_FILE_SIZE=********
UPLOAD_PATH="../uploads/"

# إعدادات OTP والتحقق
OTP_LENGTH=6
OTP_EXPIRE=300
SMS_PROVIDER="twilio"

# إعدادات Twilio
TWILIO_SID="your-twilio-account-sid"
TWILIO_TOKEN="your-twilio-auth-token"
TWILIO_PHONE="your-twilio-phone-number"

# إعدادات البريد الإلكتروني
MAIL_HOST="smtp.gmail.com"
MAIL_PORT=587
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your-email-password"
MAIL_FROM_NAME="WhatsApp Clone"
MAIL_ENCRYPTION="tls"

# إعدادات WebSocket
WEBSOCKET_HOST="localhost"
WEBSOCKET_PORT=8080
WEBSOCKET_ENABLED=true

# إعدادات الذكاء الاصطناعي
AI_ENABLED=true
OPENAI_API_KEY="your-openai-api-key"
AI_MODEL="gpt-3.5-turbo"
AI_MAX_TOKENS=150

# إعدادات المكالمات
WEBRTC_ENABLED=true
STUN_SERVER_1="stun:stun.l.google.com:19302"
STUN_SERVER_2="stun:stun1.l.google.com:19302"

# إعدادات الدفع - Stripe
STRIPE_ENABLED=true
STRIPE_PUBLIC_KEY="pk_test_your_stripe_public_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# إعدادات الدفع - PayPal
PAYPAL_ENABLED=true
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
PAYPAL_MODE="sandbox"

# إعدادات التخزين المؤقت
CACHE_ENABLED=true
CACHE_DRIVER="file"
CACHE_TTL=3600

# إعدادات السجلات
LOG_ENABLED=true
LOG_LEVEL="info"
LOG_PATH="../logs/"

# إعدادات الأمان المتقدمة
RATE_LIMIT_ENABLED=true
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_TIME=900
SESSION_TIMEOUT=3600
CSRF_PROTECTION=true
XSS_PROTECTION=true

# إعدادات Feature Flags
FEATURE_FLAGS_ENABLED=true

# إعدادات الإشعارات
PUSH_NOTIFICATIONS_ENABLED=true
FCM_SERVER_KEY="your-fcm-server-key"
APNS_CERTIFICATE_PATH="path-to-apns-certificate"
APNS_CERTIFICATE_PASS="apns-certificate-password"

# إعدادات التحليلات
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400
BACKUP_RETENTION=30
BACKUP_PATH="../backups/"

# إعدادات المنطقة الزمنية واللغة
DEFAULT_TIMEZONE="Asia/Riyadh"
DEFAULT_LANGUAGE="ar"
SUPPORTED_LANGUAGES="ar,en,fr"

# إعدادات الاشتراكات
FREE_MAX_GROUPS=5
FREE_MAX_FILE_SIZE=10
FREE_AI_REQUESTS_PER_DAY=10
FREE_CALL_DURATION_LIMIT=30

VIP_MAX_GROUPS=50
VIP_MAX_FILE_SIZE=100
VIP_AI_REQUESTS_PER_DAY=100
VIP_CALL_DURATION_LIMIT=0

ENTERPRISE_MAX_GROUPS=0
ENTERPRISE_MAX_FILE_SIZE=500
ENTERPRISE_AI_REQUESTS_PER_DAY=0
ENTERPRISE_CALL_DURATION_LIMIT=0

# إعدادات الصيانة
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="النظام تحت الصيانة. سنعود قريباً."

# إعدادات التطوير
DEBUG_MODE=true
ERROR_REPORTING=true
SHOW_ERRORS=true

# إعدادات الإنتاج (تفعل في الإنتاج فقط)
# APP_ENV="production"
# APP_DEBUG=false
# DEBUG_MODE=false
# ERROR_REPORTING=false
# SHOW_ERRORS=false
