# WhatsApp Clone Backend .htaccess Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files ".env">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<FilesMatch "\.(log|sql|md)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلدات النظام
RedirectMatch 403 ^/backend/(config|models|controllers|middleware|utils|logs|cache)/.*$

# إعدادات الأمان
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

# إعدادات CORS للتطوير
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
Header always set Access-Control-Max-Age "86400"

# معالجة طلبات OPTIONS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# توجيه طلبات API
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# توجيه رفع الملفات
RewriteRule ^uploads/(.*)$ uploads/$1 [L]

# منع الوصول المباشر لملفات PHP عدا API
RewriteCond %{REQUEST_URI} !^/backend/api/
RewriteCond %{REQUEST_URI} \.php$
RewriteRule ^(.*)$ - [F,L]

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# حد حجم الرفع
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 256M

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_strict_mode 1

# منع عرض الأخطاء في الإنتاج
php_flag display_errors Off
php_flag log_errors On
php_value error_log logs/php_errors.log

# تعيين المنطقة الزمنية
php_value date.timezone "Asia/Riyadh"

# إعدادات UTF-8
AddDefaultCharset UTF-8

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# منع الربط المباشر للصور
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif|bmp)$ [NC]
RewriteRule \.(jpg|jpeg|png|gif|bmp)$ - [F]

# صفحة خطأ مخصصة
ErrorDocument 403 /backend/errors/403.html
ErrorDocument 404 /backend/errors/404.html
ErrorDocument 500 /backend/errors/500.html
