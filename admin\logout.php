<?php
/**
 * تسجيل خروج المسؤول
 * Ad<PERSON>
 */

// تعريف ثابت الوصول
define('APP_ACCESS', true);

// تضمين الملفات المطلوبة
require_once '../backend/config/config.php';
require_once '../backend/utils/helpers.php';

// بدء الجلسة
session_start();

// تسجيل النشاط إذا كان مسجل دخول
if (isset($_SESSION['admin_id'])) {
    logActivity($_SESSION['admin_id'], 'admin_logout');
}

// مسح جميع متغيرات الجلسة
$_SESSION = array();

// حذف ملف تعريف الارتباط للجلسة إذا كان موجوداً
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?logged_out=1');
exit;
?>
