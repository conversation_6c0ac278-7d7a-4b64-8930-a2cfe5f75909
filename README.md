# 🚀 مشروع تطبيق الدردشة الاحترافي - WhatsApp Clone

## 📋 نظرة عامة
تطبيق دردشة شامل ومتقدم شبيه بـ WhatsApp مع ميزات الذكاء الاصطناعي والأمان المتقدم

## 🏗️ هيكل المشروع

```
whatsapp/
├── backend/                    # الواجهة الخلفية PHP
│   ├── api/                   # REST API endpoints
│   ├── config/                # ملفات التكوين
│   ├── models/                # نماذج قاعدة البيانات
│   ├── controllers/           # المتحكمات
│   ├── middleware/            # الوسطاء
│   ├── utils/                 # الأدوات المساعدة
│   └── uploads/               # ملفات الرفع
├── frontend/                  # تطبيق Flutter
│   ├── lib/                   # كود التطبيق
│   ├── assets/                # الموارد
│   └── android/               # إعدادات Android
├── admin/                     # لوحة تحكم المسؤول
│   ├── dashboard/             # لوحة القيادة
│   ├── users/                 # إدارة المستخدمين
│   ├── messages/              # إدارة الرسائل
│   ├── features/              # إدارة الميزات
│   └── reports/               # التقارير
├── database/                  # قاعدة البيانات
│   ├── migrations/            # ملفات الهجرة
│   ├── seeds/                 # البيانات الأولية
│   └── schema.sql             # مخطط قاعدة البيانات
└── docs/                      # الوثائق
    ├── api/                   # وثائق API
    ├── database/              # وثائق قاعدة البيانات
    └── deployment/            # دليل النشر
```

## 🔧 التقنيات المستخدمة

### Backend
- **PHP 8.1+** - الواجهة الخلفية
- **MySQL 8.0+** - قاعدة البيانات
- **JWT** - المصادقة
- **WebSocket** - الرسائل الفورية

### Frontend
- **Flutter 3.0+** - تطبيق الهاتف
- **Dart** - لغة البرمجة
- **Provider/Bloc** - إدارة الحالة

### Admin Panel
- **PHP + HTML/CSS/JS** - لوحة التحكم
- **Bootstrap 5** - التصميم
- **Chart.js** - الرسوم البيانية

## 🚀 الميزات الرئيسية

### 🔐 الأمان والخصوصية
- تشفير End-to-End للرسائل
- مصادقة ثنائية (2FA)
- قفل التطبيق ببصمة/رمز
- مراقبة الجلسات النشطة

### 💬 المحادثات
- رسائل فردية وجماعية
- رسائل صوتية ومرئية
- مشاركة الملفات والوسائط
- ردود وإعادة توجيه
- رسائل مؤقتة ومجدولة

### 📞 المكالمات
- مكالمات صوتية ومرئية
- مكالمات جماعية
- جودة متكيفة حسب الشبكة

### 🧠 الذكاء الاصطناعي
- مساعد ذكي داخل المحادثة
- تلخيص المحادثات الطويلة
- ردود تلقائية ذكية
- تحليل المشاعر

### ⚙️ إدارة ديناميكية
- نظام Feature Flags
- تفعيل/تعطيل الميزات من لوحة التحكم
- إدارة شاملة للمستخدمين والمحتوى

## 📦 متطلبات التشغيل

### الخادم
- PHP 8.1+
- MySQL 8.0+
- Apache/Nginx
- SSL Certificate
- 4GB RAM (الحد الأدنى)

### التطوير
- Flutter SDK 3.0+
- Android Studio / VS Code
- Git
- Composer (PHP)

## 🛠️ التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/whatsapp-clone.git
cd whatsapp-clone
```

### 2. إعداد Backend
```bash
cd backend
composer install
cp config/config.example.php config/config.php
# تحديث إعدادات قاعدة البيانات
```

### 3. إعداد قاعدة البيانات
```bash
mysql -u root -p < database/schema.sql
```

### 4. إعداد Flutter
```bash
cd frontend
flutter pub get
flutter run
```

## 📚 الوثائق
- [API Documentation](docs/api/README.md)
- [Database Schema](docs/database/README.md)
- [Deployment Guide](docs/deployment/README.md)

## 🤝 المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم
للدعم الفني، يرجى فتح issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.

---
**تم تطويره بـ ❤️ للمجتمع العربي**
