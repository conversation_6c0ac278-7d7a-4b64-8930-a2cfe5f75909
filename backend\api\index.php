<?php
/**
 * نقطة دخول API الرئيسية
 * Main API Entry Point
 */

// تعريف ثابت الوصول
define('APP_ACCESS', true);

// تضمين الملفات المطلوبة
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../utils/helpers.php';
require_once '../middleware/auth.php';
require_once '../middleware/cors.php';
require_once '../middleware/rate_limit.php';

// تعيين ترويسات CORS
setCorsHeaders();

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تعيين ترويسة Content-Type
header('Content-Type: application/json; charset=utf-8');

try {
    // الحصول على الطريقة والمسار
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    
    // إزالة /backend/api من المسار
    $path = str_replace('/backend/api', '', $path);
    $path = trim($path, '/');
    
    // تقسيم المسار
    $pathParts = explode('/', $path);
    $endpoint = $pathParts[0] ?? '';
    $action = $pathParts[1] ?? '';
    $id = $pathParts[2] ?? null;
    
    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    $data = array_merge($_GET, $_POST, $input);
    
    // تنظيف البيانات
    $data = sanitizeInput($data);
    
    // التحقق من حد المعدل
    $clientIP = getRealIP();
    if (!checkRateLimit($clientIP, 100, 3600)) { // 100 طلب في الساعة
        errorResponse('Too many requests. Please try again later.', 429);
    }
    
    // توجيه الطلبات حسب النقطة النهائية
    switch ($endpoint) {
        case 'auth':
            handleAuthEndpoint($method, $action, $data);
            break;
            
        case 'users':
            requireAuth();
            handleUsersEndpoint($method, $action, $id, $data);
            break;
            
        case 'messages':
            requireAuth();
            handleMessagesEndpoint($method, $action, $id, $data);
            break;
            
        case 'conversations':
            requireAuth();
            handleConversationsEndpoint($method, $action, $id, $data);
            break;
            
        case 'calls':
            requireAuth();
            handleCallsEndpoint($method, $action, $id, $data);
            break;
            
        case 'media':
            requireAuth();
            handleMediaEndpoint($method, $action, $id, $data);
            break;
            
        case 'status':
            requireAuth();
            handleStatusEndpoint($method, $action, $id, $data);
            break;
            
        case 'ai':
            requireAuth();
            handleAIEndpoint($method, $action, $data);
            break;
            
        case 'admin':
            requireAdminAuth();
            handleAdminEndpoint($method, $action, $id, $data);
            break;
            
        case 'features':
            handleFeaturesEndpoint($method, $action, $data);
            break;
            
        case '':
            // الصفحة الرئيسية للـ API
            successResponse([
                'name' => APP_NAME,
                'version' => APP_VERSION,
                'status' => 'running',
                'endpoints' => [
                    'auth' => 'Authentication endpoints',
                    'users' => 'User management',
                    'messages' => 'Messaging system',
                    'conversations' => 'Conversation management',
                    'calls' => 'Voice and video calls',
                    'media' => 'Media file handling',
                    'status' => 'Status updates',
                    'ai' => 'AI assistant features',
                    'features' => 'Feature flags'
                ]
            ], 'API is running successfully');
            break;
            
        default:
            errorResponse('Endpoint not found', 404);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("API Error: " . $e->getMessage());
    
    if (DEBUG_MODE) {
        errorResponse('Internal server error: ' . $e->getMessage(), 500);
    } else {
        errorResponse('Internal server error', 500);
    }
}

/**
 * معالجة نقاط المصادقة
 */
function handleAuthEndpoint($method, $action, $data) {
    require_once '../controllers/AuthController.php';
    $controller = new AuthController();
    
    switch ($action) {
        case 'register':
            if ($method === 'POST') {
                $controller->register($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'verify-otp':
            if ($method === 'POST') {
                $controller->verifyOTP($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'login':
            if ($method === 'POST') {
                $controller->login($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'refresh':
            if ($method === 'POST') {
                $controller->refreshToken($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'logout':
            if ($method === 'POST') {
                requireAuth();
                $controller->logout();
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'forgot-password':
            if ($method === 'POST') {
                $controller->forgotPassword($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'reset-password':
            if ($method === 'POST') {
                $controller->resetPassword($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        default:
            errorResponse('Auth action not found', 404);
    }
}

/**
 * معالجة نقاط المستخدمين
 */
function handleUsersEndpoint($method, $action, $id, $data) {
    require_once '../controllers/UserController.php';
    $controller = new UserController();
    
    switch ($action) {
        case 'profile':
            if ($method === 'GET') {
                $controller->getProfile();
            } elseif ($method === 'PUT') {
                $controller->updateProfile($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'search':
            if ($method === 'GET') {
                $controller->searchUsers($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'block':
            if ($method === 'POST') {
                $controller->blockUser($data);
            } elseif ($method === 'DELETE') {
                $controller->unblockUser($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'settings':
            if ($method === 'GET') {
                $controller->getSettings();
            } elseif ($method === 'PUT') {
                $controller->updateSettings($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'contacts':
            if ($method === 'GET') {
                $controller->getContacts();
            } elseif ($method === 'POST') {
                $controller->syncContacts($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        default:
            if ($id) {
                if ($method === 'GET') {
                    $controller->getUserById($id);
                } else {
                    errorResponse('Method not allowed', 405);
                }
            } else {
                errorResponse('User action not found', 404);
            }
    }
}

/**
 * معالجة نقاط الرسائل
 */
function handleMessagesEndpoint($method, $action, $id, $data) {
    require_once '../controllers/MessageController.php';
    $controller = new MessageController();
    
    switch ($action) {
        case 'send':
            if ($method === 'POST') {
                $controller->sendMessage($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'conversation':
            if ($method === 'GET') {
                $controller->getConversationMessages($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'mark-read':
            if ($method === 'PUT') {
                $controller->markAsRead($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'delete':
            if ($method === 'DELETE') {
                $controller->deleteMessage($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'search':
            if ($method === 'GET') {
                $controller->searchMessages($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        default:
            if ($id) {
                if ($method === 'GET') {
                    $controller->getMessageById($id);
                } elseif ($method === 'PUT') {
                    $controller->editMessage($id, $data);
                } else {
                    errorResponse('Method not allowed', 405);
                }
            } else {
                errorResponse('Message action not found', 404);
            }
    }
}

/**
 * معالجة نقاط Feature Flags
 */
function handleFeaturesEndpoint($method, $action, $data) {
    require_once '../controllers/FeatureController.php';
    $controller = new FeatureController();
    
    switch ($action) {
        case 'list':
        case '':
            if ($method === 'GET') {
                $controller->getFeatures($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        case 'check':
            if ($method === 'POST') {
                $controller->checkFeature($data);
            } else {
                errorResponse('Method not allowed', 405);
            }
            break;
            
        default:
            errorResponse('Feature action not found', 404);
    }
}

?>
