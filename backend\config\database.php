<?php
/**
 * فئة الاتصال بقاعدة البيانات
 * Database Connection Class
 */

// منع الوصول المباشر
if (!defined('APP_ACCESS')) {
    die('Direct access not allowed');
}

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    /**
     * الحصول على مثيل واحد من قاعدة البيانات (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                PDO::ATTR_PERSISTENT => true
            ];

            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // تعيين المنطقة الزمنية
            $this->connection->exec("SET time_zone = '+03:00'");
            
        } catch (PDOException $e) {
            $this->logError("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->logError("Select query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام SELECT لصف واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->logError("SelectOne query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $this->connection->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            $this->logError("Insert query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $stmt->rowCount();
            }
            return false;
        } catch (PDOException $e) {
            $this->logError("Update query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $stmt->rowCount();
            }
            return false;
        } catch (PDOException $e) {
            $this->logError("Delete query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام عام
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            $this->logError("Execute query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * الحصول على عدد الصفوف
     */
    public function count($table, $conditions = [], $params = []) {
        $query = "SELECT COUNT(*) as count FROM {$table}";
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $result = $this->selectOne($query, $params);
        return $result ? (int)$result['count'] : 0;
    }

    /**
     * التحقق من وجود سجل
     */
    public function exists($table, $conditions = [], $params = []) {
        return $this->count($table, $conditions, $params) > 0;
    }

    /**
     * إدراج أو تحديث (UPSERT)
     */
    public function upsert($table, $data, $updateFields = []) {
        try {
            $fields = array_keys($data);
            $placeholders = ':' . implode(', :', $fields);
            
            $query = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
            
            if (!empty($updateFields)) {
                $updateClause = [];
                foreach ($updateFields as $field) {
                    $updateClause[] = "{$field} = VALUES({$field})";
                }
                $query .= " ON DUPLICATE KEY UPDATE " . implode(', ', $updateClause);
            }
            
            return $this->insert($query, $data);
        } catch (Exception $e) {
            $this->logError("Upsert failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تسجيل الأخطاء
     */
    private function logError($message) {
        if (LOG_ENABLED) {
            $logFile = LOG_PATH . 'database_' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            $logMessage = "[{$timestamp}] ERROR: {$message}" . PHP_EOL;
            
            if (!is_dir(LOG_PATH)) {
                mkdir(LOG_PATH, 0755, true);
            }
            
            file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
        }
    }

    /**
     * تنظيف البيانات
     */
    public function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        
        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }

    /**
     * إغلاق الاتصال
     */
    public function close() {
        $this->connection = null;
    }

    /**
     * منع استنساخ الكائن
     */
    private function __clone() {}

    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * دالة مساعدة للحصول على قاعدة البيانات
 */
function getDB() {
    return Database::getInstance();
}

?>
